# src/api/v1/user_routes.py
"""User Management API Routes for Ultimate Electrical Designer.

This module provides user management endpoints including CRUD operations,
user profile management, and administrative user functions.
"""

from typing import Dict, Any, List

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import JSONResponse

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.unified_performance_monitor import monitor_api_performance
from src.core.schemas.general.user_schemas import (
    UserCreateSchema,
    UserReadSchema,
    UserUpdateSchema,
    UserPaginatedResponseSchema,
    UserSummarySchema,
)
from src.core.schemas.error import ErrorResponseSchema
from src.core.security.enhanced_dependencies import (
    get_current_user,
    require_authenticated_user,
    require_admin_user,
)
from src.core.services.general.user_service import UserService
from src.core.services.dependencies import get_user_service
from src.core.utils.crud_endpoint_factory import create_user_crud_router


# Create the main user management router
router = APIRouter(
    prefix="/users",
    tags=["User Management"],
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "model": ErrorResponseSchema,
            "description": "Authentication required"
        },
        status.HTTP_403_FORBIDDEN: {
            "model": ErrorResponseSchema,
            "description": "Insufficient permissions"
        }
    }
)

# Create CRUD router using the factory
crud_router = create_user_crud_router(get_user_service)

# Include the CRUD router with admin protection
router.include_router(
    crud_router,
    dependencies=[Depends(require_admin_user)]
)


@router.get(
    "/me",
    response_model=UserReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Get Current User Profile",
    description="Get the profile of the currently authenticated user",
    responses={
        status.HTTP_200_OK: {
            "description": "Current user profile",
            "model": UserReadSchema
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema
        }
    }
)
@handle_api_errors("get_current_user_profile")
@monitor_api_performance("get_current_user_profile")
async def get_current_user_profile(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    user_service: UserService = Depends(get_user_service)
) -> UserReadSchema:
    """Get the profile of the currently authenticated user.
    
    This endpoint allows users to retrieve their own profile information
    without requiring administrative privileges.
    
    Args:
        current_user: Current authenticated user
        user_service: User service dependency
        
    Returns:
        UserReadSchema: Current user's profile data
        
    Raises:
        HTTPException: If user not found or access denied
    """
    user_id = current_user.get("id")
    logger.debug(f"Getting profile for current user: {user_id}")
    
    try:
        user_profile = user_service.get_by_id(user_id)
        if not user_profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User profile not found"
            )
        
        logger.debug(f"Profile retrieved for user: {user_id}")
        return user_profile
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get profile for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user profile"
        )


@router.put(
    "/me",
    response_model=UserReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Update Current User Profile",
    description="Update the profile of the currently authenticated user",
    responses={
        status.HTTP_200_OK: {
            "description": "Profile updated successfully",
            "model": UserReadSchema
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid update data",
            "model": ErrorResponseSchema
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema
        }
    }
)
@handle_api_errors("update_current_user_profile")
@monitor_api_performance("update_current_user_profile")
async def update_current_user_profile(
    user_update: UserUpdateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    user_service: UserService = Depends(get_user_service)
) -> UserReadSchema:
    """Update the profile of the currently authenticated user.
    
    This endpoint allows users to update their own profile information.
    Users cannot modify their role or admin status through this endpoint.
    
    Args:
        user_update: User update data
        current_user: Current authenticated user
        user_service: User service dependency
        
    Returns:
        UserReadSchema: Updated user profile
        
    Raises:
        HTTPException: If update fails or access denied
    """
    user_id = current_user.get("id")
    logger.info(f"Updating profile for current user: {user_id}")
    
    try:
        # Prevent users from modifying their role
        if hasattr(user_update, 'role') and user_update.role is not None:
            logger.warning(f"User {user_id} attempted to modify their role")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot modify your own role"
            )
        
        # Update user profile
        updated_user = user_service.update(user_id, user_update)
        
        logger.info(f"Profile updated successfully for user: {user_id}")
        return updated_user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update profile for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )


@router.get(
    "/summary",
    response_model=List[UserSummarySchema],
    status_code=status.HTTP_200_OK,
    summary="Get Users Summary",
    description="Get a summary list of all users (admin only)",
    responses={
        status.HTTP_200_OK: {
            "description": "List of user summaries",
            "model": List[UserSummarySchema]
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Admin privileges required",
            "model": ErrorResponseSchema
        }
    }
)
@handle_api_errors("get_users_summary")
@monitor_api_performance("get_users_summary")
async def get_users_summary(
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of users to return"),
    current_user: Dict[str, Any] = Depends(require_admin_user),
    user_service: UserService = Depends(get_user_service)
) -> List[UserSummarySchema]:
    """Get a summary list of all users.
    
    This endpoint provides a condensed view of all users in the system,
    suitable for administrative dashboards and user selection interfaces.
    
    Args:
        limit: Maximum number of users to return
        current_user: Current authenticated admin user
        user_service: User service dependency
        
    Returns:
        List[UserSummarySchema]: List of user summaries
        
    Raises:
        HTTPException: If access denied or operation fails
    """
    logger.debug(f"Getting users summary (limit: {limit}) for admin: {current_user.get('id')}")
    
    try:
        # Get paginated users and convert to summary format
        users_response = user_service.get_all(page=1, page_size=limit)
        
        # Convert to summary format (would need to implement in service)
        user_summaries = []
        for user in users_response.items:
            summary = UserSummarySchema(
                id=user.id,
                name=user.name,
                email=user.email,
                role=user.role,
                is_active=user.is_active,
                last_login=user.last_login,
                created_at=user.created_at
            )
            user_summaries.append(summary)
        
        logger.debug(f"Retrieved {len(user_summaries)} user summaries")
        return user_summaries
        
    except Exception as e:
        logger.error(f"Failed to get users summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve users summary"
        )


@router.get(
    "/{user_id}/profile",
    response_model=UserReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Get User Profile by ID",
    description="Get detailed profile of a specific user (admin only)",
    responses={
        status.HTTP_200_OK: {
            "description": "User profile data",
            "model": UserReadSchema
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "User not found",
            "model": ErrorResponseSchema
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Admin privileges required",
            "model": ErrorResponseSchema
        }
    }
)
@handle_api_errors("get_user_profile_by_id")
@monitor_api_performance("get_user_profile_by_id")
async def get_user_profile_by_id(
    user_id: int,
    current_user: Dict[str, Any] = Depends(require_admin_user),
    user_service: UserService = Depends(get_user_service)
) -> UserReadSchema:
    """Get detailed profile of a specific user.
    
    This endpoint allows administrators to view detailed profile information
    for any user in the system.
    
    Args:
        user_id: ID of the user to retrieve
        current_user: Current authenticated admin user
        user_service: User service dependency
        
    Returns:
        UserReadSchema: User profile data
        
    Raises:
        HTTPException: If user not found or access denied
    """
    logger.debug(f"Getting profile for user {user_id} by admin: {current_user.get('id')}")
    
    try:
        user_profile = user_service.get_by_id(user_id)
        if not user_profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User with ID {user_id} not found"
            )
        
        logger.debug(f"Profile retrieved for user: {user_id}")
        return user_profile
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get profile for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user profile"
        )


@router.post(
    "/{user_id}/activate",
    response_model=UserReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Activate User Account",
    description="Activate a user account (admin only)",
    responses={
        status.HTTP_200_OK: {
            "description": "User account activated",
            "model": UserReadSchema
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "User not found",
            "model": ErrorResponseSchema
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Admin privileges required",
            "model": ErrorResponseSchema
        }
    }
)
@handle_api_errors("activate_user_account")
@monitor_api_performance("activate_user_account")
async def activate_user_account(
    user_id: int,
    current_user: Dict[str, Any] = Depends(require_admin_user),
    user_service: UserService = Depends(get_user_service)
) -> UserReadSchema:
    """Activate a user account.
    
    This endpoint allows administrators to activate a user account,
    enabling the user to log in and access the system.
    
    Args:
        user_id: ID of the user to activate
        current_user: Current authenticated admin user
        user_service: User service dependency
        
    Returns:
        UserReadSchema: Updated user data
        
    Raises:
        HTTPException: If user not found or operation fails
    """
    admin_id = current_user.get("id")
    logger.info(f"Activating user {user_id} by admin: {admin_id}")
    
    try:
        # Update user to set is_active = True
        user_update = UserUpdateSchema(is_active=True)
        updated_user = user_service.update(user_id, user_update)
        
        logger.info(f"User {user_id} activated successfully by admin: {admin_id}")
        return updated_user
        
    except Exception as e:
        logger.error(f"Failed to activate user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to activate user account"
        )


@router.post(
    "/{user_id}/deactivate",
    response_model=UserReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Deactivate User Account",
    description="Deactivate a user account (admin only)",
    responses={
        status.HTTP_200_OK: {
            "description": "User account deactivated",
            "model": UserReadSchema
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "User not found",
            "model": ErrorResponseSchema
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Admin privileges required",
            "model": ErrorResponseSchema
        }
    }
)
@handle_api_errors("deactivate_user_account")
@monitor_api_performance("deactivate_user_account")
async def deactivate_user_account(
    user_id: int,
    current_user: Dict[str, Any] = Depends(require_admin_user),
    user_service: UserService = Depends(get_user_service)
) -> UserReadSchema:
    """Deactivate a user account.
    
    This endpoint allows administrators to deactivate a user account,
    preventing the user from logging in while preserving their data.
    
    Args:
        user_id: ID of the user to deactivate
        current_user: Current authenticated admin user
        user_service: User service dependency
        
    Returns:
        UserReadSchema: Updated user data
        
    Raises:
        HTTPException: If user not found or operation fails
    """
    admin_id = current_user.get("id")
    logger.info(f"Deactivating user {user_id} by admin: {admin_id}")
    
    try:
        # Prevent admin from deactivating themselves
        if user_id == admin_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot deactivate your own account"
            )
        
        # Update user to set is_active = False
        user_update = UserUpdateSchema(is_active=False)
        updated_user = user_service.update(user_id, user_update)
        
        logger.info(f"User {user_id} deactivated successfully by admin: {admin_id}")
        return updated_user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to deactivate user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate user account"
        )
