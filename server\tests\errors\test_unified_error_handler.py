# tests/unit/test_errors/test_unified_error_handler.py
"""
Comprehensive tests for unified error handling system.

This module tests the unified error handler including error contexts,
error handling results, exception handling, decorators, and error tracking.
"""

import os
import sys
import logging
from unittest.mock import Mock, patch, MagicMock
from contextlib import contextmanager

import pytest
from fastapi import Request, HTTPException

# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
sys.path.insert(0, server_path)

from src.core.errors.unified_error_handler import (
    ErrorContext,
    ErrorHandlingResult,
    UnifiedErrorHandler,
    unified_error_handler,
    get_unified_error_handler,
    handle_service_errors,
    handle_repository_errors,
    handle_calculation_errors,
)
from src.core.errors.exceptions import (
    BaseApplicationException,
    NotFoundError,
    DataValidationError,
    InvalidInputError,
    ServiceError,
    DatabaseError,
    CalculationError,
    DuplicateEntryError,
)

pytestmark = [pytest.mark.unit]


class TestErrorContext:
    """Test suite for ErrorContext enum."""

    def test_error_context_values(self):
        """Test ErrorContext enum values."""
        assert ErrorContext.API.value == "API"
        assert ErrorContext.SERVICE.value == "Service Layer"
        assert ErrorContext.REPOSITORY.value == "Repository"
        assert ErrorContext.CALCULATION.value == "Calculation"
        assert ErrorContext.MIDDLEWARE.value == "Middleware"
        assert ErrorContext.VALIDATION.value == "Validation"
        assert ErrorContext.DATABASE.value == "Database"
        assert ErrorContext.SECURITY.value == "Security"

    def test_error_context_iteration(self):
        """Test that ErrorContext can be iterated."""
        contexts = list(ErrorContext)
        assert len(contexts) >= 8
        assert ErrorContext.API in contexts
        assert ErrorContext.SERVICE in contexts
        assert ErrorContext.REPOSITORY in contexts
        assert ErrorContext.CALCULATION in contexts
        assert ErrorContext.MIDDLEWARE in contexts
        assert ErrorContext.VALIDATION in contexts
        assert ErrorContext.DATABASE in contexts
        assert ErrorContext.SECURITY in contexts


class TestErrorHandlingResult:
    """Test suite for ErrorHandlingResult class."""

    def test_error_handling_result_creation_minimal(self):
        """Test ErrorHandlingResult creation with minimal data."""
        error_response = Mock()
        error_response.detail = "Test error"

        result = ErrorHandlingResult(
            error_response=error_response,
            http_status_code=400,
        )

        assert result.error_response == error_response
        assert result.http_status_code == 400
        assert result.should_log is True  # Default
        assert result.log_level == logging.ERROR  # Default
        assert result.context_data == {}  # Default

    def test_error_handling_result_creation_complete(self):
        """Test ErrorHandlingResult creation with complete data."""
        error_response = Mock()
        error_response.detail = "Validation failed"
        context_data = {"user_id": "123", "operation": "create_user"}

        result = ErrorHandlingResult(
            error_response=error_response,
            http_status_code=422,
            should_log=False,
            log_level=logging.WARNING,
            context_data=context_data,
        )

        assert result.error_response == error_response
        assert result.http_status_code == 422
        assert result.should_log is False
        assert result.log_level == logging.WARNING
        assert result.context_data == context_data

    def test_error_handling_result_defaults(self):
        """Test ErrorHandlingResult default values."""
        error_response = Mock()

        result = ErrorHandlingResult(
            error_response=error_response,
            http_status_code=500,
        )

        assert result.should_log is True
        assert result.log_level == logging.ERROR
        assert result.context_data == {}


class TestUnifiedErrorHandler:
    """Test suite for UnifiedErrorHandler class."""

    def test_unified_error_handler_initialization_defaults(self):
        """Test UnifiedErrorHandler initialization with defaults."""
        handler = UnifiedErrorHandler()

        assert handler.enable_debug_mode is False
        assert handler.include_stack_trace is False
        assert handler.max_error_message_length == 1000
        assert handler.enable_error_tracking is True
        assert handler.error_counts == {}
        assert handler.error_history == []

    def test_unified_error_handler_initialization_custom(self):
        """Test UnifiedErrorHandler initialization with custom values."""
        handler = UnifiedErrorHandler(
            enable_debug_mode=True,
            include_stack_trace=True,
            max_error_message_length=500,
            enable_error_tracking=False,
        )

        assert handler.enable_debug_mode is True
        assert handler.include_stack_trace is True
        assert handler.max_error_message_length == 500
        assert handler.enable_error_tracking is False

    def test_handle_application_exception(self):
        """Test handling BaseApplicationException."""
        handler = UnifiedErrorHandler()
        exception = NotFoundError(
            code="TEST_404",
            detail="Resource not found",
            category="ClientError",
            status_code=404,
        )
        context = ErrorContext.API

        result = handler.handle_exception(exception, context)

        assert isinstance(result, ErrorHandlingResult)
        assert result.http_status_code == 404
        assert result.error_response.detail == "Resource not found"
        assert result.should_log is True
        assert result.log_level == logging.WARNING  # NotFoundError gets WARNING level

    def test_handle_application_exception_with_request(self):
        """Test handling exception with request context."""
        handler = UnifiedErrorHandler()
        exception = ServiceError("Service failed")
        context = ErrorContext.SERVICE

        # Mock request
        mock_request = Mock(spec=Request)
        mock_request.url.path = "/api/v1/test"

        result = handler.handle_exception(exception, context, mock_request)

        assert result.context_data["request_path"] == "/api/v1/test"
        assert result.context_data["exception_type"] == "ServiceError"
        assert result.context_data["context"] == "Service Layer"

    def test_handle_application_exception_with_additional_context(self):
        """Test handling exception with additional context."""
        handler = UnifiedErrorHandler()
        exception = DataValidationError({"field": ["error"]})
        context = ErrorContext.API
        additional_context = {"user_id": "123", "operation": "create"}

        result = handler.handle_exception(
            exception, context, additional_context=additional_context
        )

        assert result.context_data["user_id"] == "123"
        assert result.context_data["operation"] == "create"

    def test_handle_generic_exception(self):
        """Test handling generic Python exception."""
        handler = UnifiedErrorHandler()
        exception = RuntimeError("Unexpected runtime error")
        context = ErrorContext.SERVICE

        result = handler.handle_exception(exception, context)

        assert result.http_status_code == 500
        assert "An unexpected internal error occurred" in result.error_response.detail
        assert result.log_level == logging.ERROR

    def test_handle_exception_debug_mode(self):
        """Test handling exception in debug mode."""
        handler = UnifiedErrorHandler(enable_debug_mode=True, include_stack_trace=True)
        exception = RuntimeError("Debug error")
        context = ErrorContext.API

        result = handler.handle_exception(exception, context)

        # In debug mode, should include more details
        assert "Debug error" in result.error_response.detail

    def test_error_tracking_enabled(self):
        """Test error tracking when enabled."""
        handler = UnifiedErrorHandler(enable_error_tracking=True)
        exception = NotFoundError(code="TEST_404", detail="Not found")
        context = ErrorContext.API

        initial_count = len(handler.error_history)

        handler.handle_exception(exception, context)

        # Should track the error
        assert len(handler.error_history) == initial_count + 1
        assert "API:NotFoundError" in handler.error_counts
        assert handler.error_counts["API:NotFoundError"] == 1

    def test_error_tracking_disabled(self):
        """Test error tracking when disabled."""
        handler = UnifiedErrorHandler(enable_error_tracking=False)
        exception = ServiceError("Service error")
        context = ErrorContext.SERVICE

        initial_count = len(handler.error_history)

        handler.handle_exception(exception, context)

        # Should not track the error
        assert len(handler.error_history) == initial_count
        assert len(handler.error_counts) == 0

    def test_get_error_statistics(self):
        """Test get_error_statistics method."""
        handler = UnifiedErrorHandler(enable_error_tracking=True)

        # Add some test errors
        handler.handle_exception(
            NotFoundError(code="404", detail="Not found"), ErrorContext.API
        )
        handler.handle_exception(ServiceError("Service error"), ErrorContext.SERVICE)
        handler.handle_exception(
            NotFoundError(code="404", detail="Another not found"), ErrorContext.API
        )

        stats = handler.get_error_statistics()

        assert "error_counts" in stats
        assert "recent_errors" in stats
        assert "error_types" in stats
        assert stats["error_counts"]["total"] == 3
        assert stats["error_counts"]["API:NotFoundError"] == 2
        assert stats["error_counts"]["Service Layer:ServiceError"] == 1
        assert len(stats["recent_errors"]) == 3
        assert "NotFoundError" in stats["error_types"]
        assert "ServiceError" in stats["error_types"]

    def test_clear_error_history(self):
        """Test clear_error_history method."""
        handler = UnifiedErrorHandler(enable_error_tracking=True)

        # Add some errors
        handler.handle_exception(ServiceError("Error 1"), ErrorContext.SERVICE)
        handler.handle_exception(ServiceError("Error 2"), ErrorContext.SERVICE)

        assert len(handler.error_history) == 2
        assert len(handler.error_counts) == 1

        handler.clear_error_history()

        assert len(handler.error_history) == 0
        assert len(handler.error_counts) == 0

    def test_error_context_manager(self):
        """Test error_context context manager."""
        handler = UnifiedErrorHandler()

        # Test successful operation
        with handler.error_context(ErrorContext.SERVICE, "test_operation"):
            result = "success"

        # Should complete without issues
        assert result == "success"

    def test_error_context_manager_with_exception(self):
        """Test error_context context manager with exception."""
        handler = UnifiedErrorHandler()

        with pytest.raises(HTTPException) as exc_info:
            with handler.error_context(ErrorContext.SERVICE, "failing_operation"):
                raise RuntimeError("Test error")

        # Should convert to HTTPException
        assert exc_info.value.status_code == 500
        assert "An unexpected internal error occurred" in str(exc_info.value.detail)

    def test_error_context_manager_with_application_exception(self):
        """Test error_context context manager with application exception."""
        handler = UnifiedErrorHandler()

        with pytest.raises(HTTPException) as exc_info:
            with handler.error_context(ErrorContext.API, "api_operation"):
                raise NotFoundError(
                    code="404",
                    detail="Resource not found",
                    category="ClientError",
                    status_code=404,
                )

        assert exc_info.value.status_code == 404
        assert "Resource not found" in str(exc_info.value.detail)

    def test_error_context_manager_with_additional_context(self):
        """Test error_context context manager with additional context."""
        handler = UnifiedErrorHandler(enable_error_tracking=True)
        additional_context = {"user_id": "123"}

        with pytest.raises(HTTPException):
            with handler.error_context(
                ErrorContext.SERVICE, "test_op", additional_context
            ):
                raise ServiceError("Test service error")

        # Check that context was recorded
        assert len(handler.error_history) == 1
        error_record = handler.error_history[0]
        assert error_record["context_data"]["user_id"] == "123"
        assert error_record["context_data"]["operation"] == "test_op"

    def test_message_length_truncation(self):
        """Test error message length truncation."""
        handler = UnifiedErrorHandler(max_error_message_length=50)
        long_message = "A" * 100  # 100 character message
        exception = ServiceError(long_message)

        result = handler.handle_exception(exception, ErrorContext.SERVICE)

        # Message should be truncated
        assert len(result.error_response.detail) <= 50

    def test_different_log_levels_for_exception_types(self):
        """Test different log levels for different exception types."""
        handler = UnifiedErrorHandler()

        # Test WARNING level exceptions
        warning_exceptions = [
            DataValidationError({}),
            InvalidInputError("Invalid input"),
            NotFoundError(code="404", detail="Not found"),
        ]

        for exception in warning_exceptions:
            result = handler.handle_exception(exception, ErrorContext.API)
            assert result.log_level == logging.WARNING

        # Test ERROR level exceptions
        error_exceptions = [
            DatabaseError("DB error"),
            ServiceError("Service error"),
        ]

        for exception in error_exceptions:
            result = handler.handle_exception(exception, ErrorContext.SERVICE)
            assert result.log_level == logging.ERROR

    def test_global_instance_access(self):
        """Test global unified_error_handler instance."""
        assert unified_error_handler is not None
        assert isinstance(unified_error_handler, UnifiedErrorHandler)

    def test_get_unified_error_handler_dependency(self):
        """Test get_unified_error_handler dependency function."""
        handler = get_unified_error_handler()

        assert handler is not None
        assert isinstance(handler, UnifiedErrorHandler)
        assert handler is unified_error_handler  # Should return the same instance


class TestErrorHandlerDecorators:
    """Test suite for error handler decorators."""

    def test_handle_service_errors_decorator_success(self):
        """Test handle_service_errors decorator with successful operation."""

        @handle_service_errors("test_service_operation")
        def successful_service_function():
            return "success"

        result = successful_service_function()
        assert result == "success"

    def test_handle_service_errors_decorator_with_application_exception(self):
        """Test handle_service_errors decorator with application exception."""

        @handle_service_errors("failing_service_operation")
        def failing_service_function():
            raise NotFoundError(code="404", detail="Resource not found")

        # Application exceptions should be re-raised as-is
        with pytest.raises(NotFoundError):
            failing_service_function()

    def test_handle_service_errors_decorator_with_generic_exception(self):
        """Test handle_service_errors decorator with generic exception."""

        @handle_service_errors("generic_error_operation")
        def generic_error_function():
            raise ValueError("Generic error")

        # Generic exceptions should be converted to ServiceError
        with pytest.raises(ServiceError) as exc_info:
            generic_error_function()

        assert "Service operation failed" in str(exc_info.value.detail)

    def test_handle_repository_errors_decorator_success(self):
        """Test handle_repository_errors decorator with successful operation."""

        @handle_repository_errors("user")
        def successful_repository_function():
            return {"id": 1, "name": "test"}

        result = successful_repository_function()
        assert result["name"] == "test"

    def test_handle_repository_errors_decorator_with_application_exception(self):
        """Test handle_repository_errors decorator with application exception."""

        @handle_repository_errors("user")
        def failing_repository_function():
            raise DatabaseError("Database connection failed")

        # Application exceptions should be re-raised as-is
        with pytest.raises(DatabaseError):
            failing_repository_function()

    def test_handle_repository_errors_decorator_with_integrity_error(self):
        """Test handle_repository_errors decorator with SQLAlchemy IntegrityError."""
        from sqlalchemy.exc import IntegrityError

        @handle_repository_errors("user")
        def integrity_error_function():
            # Mock IntegrityError
            raise IntegrityError("statement", "params", "orig")

        # Should convert to DuplicateEntryError
        with pytest.raises(DuplicateEntryError) as exc_info:
            integrity_error_function()

        assert "A user with the given unique constraint already exists" in str(
            exc_info.value.detail
        )

    def test_handle_repository_errors_decorator_with_no_result_found(self):
        """Test handle_repository_errors decorator with SQLAlchemy NoResultFound."""
        from sqlalchemy.exc import NoResultFound

        @handle_repository_errors("project")
        def no_result_function():
            raise NoResultFound("No result found")

        # Should convert to NotFoundError
        with pytest.raises(NotFoundError) as exc_info:
            no_result_function()

        assert "Project not found" in str(exc_info.value.detail)
        assert exc_info.value.code == "PROJECT_NOT_FOUND"

    def test_handle_repository_errors_decorator_with_generic_sqlalchemy_error(self):
        """Test handle_repository_errors decorator with generic SQLAlchemy error."""
        from sqlalchemy.exc import SQLAlchemyError

        @handle_repository_errors("component")
        def sqlalchemy_error_function():
            raise SQLAlchemyError("Generic SQLAlchemy error")

        # Should convert to DatabaseError
        with pytest.raises(DatabaseError) as exc_info:
            sqlalchemy_error_function()

        assert "Database operation failed for component" in str(exc_info.value.detail)

    def test_handle_repository_errors_decorator_with_generic_exception(self):
        """Test handle_repository_errors decorator with generic exception."""

        @handle_repository_errors("entity")
        def generic_error_function():
            raise ConnectionError("Connection failed")

        # Generic exceptions should be converted to ServiceError
        with pytest.raises(ServiceError) as exc_info:
            generic_error_function()

        assert "Repository operation failed" in str(exc_info.value.detail)

    def test_handle_calculation_errors_decorator_success(self):
        """Test handle_calculation_errors decorator with successful operation."""

        @handle_calculation_errors("heat_loss")
        def successful_calculation():
            return {"result": 42.5, "unit": "W"}

        result = successful_calculation()
        assert result["result"] == 42.5

    def test_handle_calculation_errors_decorator_with_exception(self):
        """Test handle_calculation_errors decorator with exception."""

        @handle_calculation_errors("power_calculation")
        def failing_calculation():
            raise ZeroDivisionError("Division by zero")

        # Should convert to CalculationError
        with pytest.raises(CalculationError) as exc_info:
            failing_calculation()

        assert "Calculation failed" in str(exc_info.value.detail)

    def test_decorator_function_metadata_preservation(self):
        """Test that decorators preserve function metadata."""

        @handle_service_errors("test_operation")
        def test_function():
            """Test function docstring."""
            return "test"

        # Function name should be preserved (though wrapper might change it)
        result = test_function()
        assert result == "test"

    def test_decorator_with_function_arguments(self):
        """Test decorators work with functions that have arguments."""

        @handle_service_errors("parameterized_operation")
        def parameterized_function(x, y, z=None):
            if z is None:
                return x + y
            return x + y + z

        # Test with positional arguments
        result1 = parameterized_function(1, 2)
        assert result1 == 3

        # Test with keyword arguments
        result2 = parameterized_function(1, 2, z=3)
        assert result2 == 6

    def test_decorator_error_context_tracking(self):
        """Test that decorators properly track error context."""
        # Clear any existing error history
        unified_error_handler.clear_error_history()

        @handle_service_errors("tracked_operation")
        def tracked_function():
            raise ValueError("Tracked error")

        with pytest.raises(ServiceError):
            tracked_function()

        # Check that error was tracked with proper context
        stats = unified_error_handler.get_error_statistics()
        assert stats["error_counts"]["total"] >= 1

        # Find our error in the history
        service_errors = [
            error
            for error in unified_error_handler.error_history
            if error["context_data"].get("operation") == "tracked_operation"
        ]
        assert len(service_errors) >= 1

    def test_nested_decorators(self):
        """Test nested error handling decorators."""

        @handle_service_errors("outer_service")
        def outer_function():
            return inner_function()

        @handle_repository_errors("inner_entity")
        def inner_function():
            raise ValueError("Inner error")

        # Should handle the error at the repository level first
        with pytest.raises(ServiceError):
            outer_function()

    def test_decorator_operation_name_defaults(self):
        """Test decorator operation name defaults."""

        @handle_service_errors()  # No operation name provided
        def default_name_function():
            raise ValueError("Default name test")

        # Should use default operation name
        with pytest.raises(ServiceError):
            default_name_function()

    def test_decorator_entity_name_defaults(self):
        """Test repository decorator entity name defaults."""

        @handle_repository_errors()  # No entity name provided
        def default_entity_function():
            raise ValueError("Default entity test")

        # Should use default entity name "resource"
        with pytest.raises(ServiceError):
            default_entity_function()

    def test_calculation_decorator_type_defaults(self):
        """Test calculation decorator type defaults."""

        @handle_calculation_errors()  # No calculation type provided
        def default_calculation_function():
            raise ValueError("Default calculation test")

        # Should use default calculation type "calculation"
        with pytest.raises(CalculationError):
            default_calculation_function()
