# src/core/validation/input_validators.py
"""Input Validation Module for Ultimate Electrical Designer Backend.

This module provides comprehensive input validation including:
- XSS protection and sanitization
- Unicode security validation
- JSON payload size and depth validation
- General security validation
"""

import html
import json
import re
import unicodedata
from typing import Any, Optional

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_validation_errors


# Import unified security system for enhanced validation
from src.core.security.unified_security_validator import (
    UnifiedSecurityValidator,
    SecurityContext,
    SecurityLevel,
    validate_and_sanitize_input,
    get_unified_security_validator,
)

logger.info("Unified security system available for input validation")

# Check if unified security is available
try:
    from src.core.security.unified_security_validator import UnifiedSecurityValidator

    UNIFIED_SECURITY_AVAILABLE = True
except ImportError:
    UNIFIED_SECURITY_AVAILABLE = False


class ValidationError(Exception):
    """Custom exception for validation errors."""


class InputValidator:
    """Base input validator class providing common validation functionality."""

    def __init__(self):
        self.logger = logger

    def validate(self, data: Any) -> bool:
        """Base validation method that must be implemented by subclasses.

        This method enforces that subclasses implement their own validation logic
        rather than relying on a default implementation.

        Args:
            data: The data to validate

        Returns:
            bool: True if validation passes, False otherwise

        Raises:
            NotImplementedError: Always raised to enforce subclass implementation

        """
        raise NotImplementedError(
            "Subclasses must implement the validate method with specific validation logic"
        )

    def _validate_basic_requirements(self, data: Any) -> bool:
        """Helper method for common validation patterns that subclasses can use.

        Args:
            data: The data to validate

        Returns:
            bool: True if basic validation passes, False otherwise

        """
        try:
            # Basic validation: check if data is not None
            if data is None:
                self.logger.debug("Validation failed: data is None")
                return False

            # For string data, check if it's not empty after stripping
            if isinstance(data, str):
                if not data.strip():
                    self.logger.debug("Validation failed: empty string")
                    return False

            # For collections, check if they're not empty
            if isinstance(data, (list, dict, tuple, set)):
                if len(data) == 0:
                    self.logger.debug("Validation failed: empty collection")
                    return False

            # Basic validation passed
            self.logger.debug("Base validation passed")
            return True

        except Exception as e:
            self.logger.error(f"Base validation error: {e}")
            return False

    @handle_validation_errors("base_input_sanitization")
    def sanitize(self, data: Any) -> Any:
        """Base sanitization method to be overridden by subclasses.

        Args:
            data: The data to sanitize

        Returns:
            Any: The sanitized data

        """
        return data


class XSSValidator(InputValidator):
    """XSS (Cross-Site Scripting) validator and sanitizer.

    Detects and sanitizes potentially dangerous XSS patterns in input data.
    """

    def __init__(self):
        super().__init__()

        # XSS patterns to detect and sanitize
        self.xss_patterns = [
            re.compile(r"<script[^>]*>.*?</script>", re.IGNORECASE | re.DOTALL),
            re.compile(r"javascript:", re.IGNORECASE),
            re.compile(r"on\w+\s*=", re.IGNORECASE),
            re.compile(r"<iframe[^>]*>.*?</iframe>", re.IGNORECASE | re.DOTALL),
            re.compile(r"<object[^>]*>.*?</object>", re.IGNORECASE | re.DOTALL),
            re.compile(r"<embed[^>]*>", re.IGNORECASE),
            re.compile(r"<svg[^>]*>.*?</svg>", re.IGNORECASE | re.DOTALL),
            re.compile(r"<link[^>]*>", re.IGNORECASE),
            re.compile(r"<meta[^>]*>", re.IGNORECASE),
            re.compile(r"<style[^>]*>.*?</style>", re.IGNORECASE | re.DOTALL),
            re.compile(r"expression\s*\(", re.IGNORECASE),
            re.compile(r"url\s*\(", re.IGNORECASE),
            re.compile(r"@import", re.IGNORECASE),
        ]

        # Dangerous attributes
        self.dangerous_attributes = {
            "onload",
            "onerror",
            "onclick",
            "onmouseover",
            "onmouseout",
            "onfocus",
            "onblur",
            "onchange",
            "onsubmit",
            "onreset",
            "onselect",
            "onkeydown",
            "onkeyup",
            "onkeypress",
        }

    def validate(self, data: Any) -> bool:
        """Validate data for XSS patterns.

        Args:
            data: The data to validate (string, dict, list, or other)

        Returns:
            bool: True if no XSS patterns detected, False otherwise

        """
        try:
            if isinstance(data, str):
                return self._validate_string(data)
            if isinstance(data, dict):
                return all(self.validate(value) for value in data.values())
            if isinstance(data, list):
                return all(self.validate(item) for item in data)
            return True  # Non-string data is considered safe
        except Exception as e:
            self.logger.error(f"XSS validation error: {e}")
            return False

    def _validate_string(self, text: str) -> bool:
        """Validate a string for XSS patterns.

        Args:
            text: The string to validate

        Returns:
            bool: True if no XSS patterns detected, False otherwise

        """
        if not text:
            return True

        # Check for dangerous patterns
        for pattern in self.xss_patterns:
            if pattern.search(text):
                self.logger.warning(f"Detected XSS pattern '{pattern.pattern}' in input data")
                return False

        # Check for dangerous attributes
        text_lower = text.lower()
        for attr in self.dangerous_attributes:
            if attr in text_lower:
                self.logger.warning(f"Detected dangerous attribute '{attr}' in input data")
                return False

        return True

    @handle_validation_errors("xss_sanitization")
    def sanitize(self, data: Any) -> Any:
        """Sanitize data by removing XSS patterns.

        Args:
            data: The data to sanitize

        Returns:
            Any: The sanitized data

        """
        if isinstance(data, str):
            return self._sanitize_string(data)
        if isinstance(data, dict):
            return {key: self.sanitize(value) for key, value in data.items()}
        if isinstance(data, list):
            return [self.sanitize(item) for item in data]
        return data

    def _sanitize_string(self, text: str) -> str:
        """Sanitize a string by removing dangerous patterns and HTML encoding.

        Args:
            text: The string to sanitize

        Returns:
            str: The sanitized string

        """
        if not text:
            return text

        # Remove dangerous patterns
        for pattern in self.xss_patterns:
            text = pattern.sub("", text)

        # Remove dangerous attributes
        for attr in self.dangerous_attributes:
            # Remove the attribute and its value
            text = re.sub(
                rf"{attr}\s*=\s*['\"][^'\"]*['\"]", "", text, flags=re.IGNORECASE
            )
            text = re.sub(rf"{attr}\s*=\s*[^\s>]*", "", text, flags=re.IGNORECASE)

        # HTML encode the remaining content
        text = html.escape(text, quote=True)

        return text


class UnicodeValidator(InputValidator):
    """Unicode security validator.

    Validates and sanitizes Unicode characters that could be used for security attacks.
    """

    def __init__(self):
        super().__init__()

        # Dangerous Unicode characters
        self.dangerous_unicode = {
            "\u0000",  # Null byte
            "\u202e",  # Right-to-left override
            "\ufeff",  # Byte order mark
            "\u2028",  # Line separator
            "\u2029",  # Paragraph separator
            "\u00ad",  # Soft hyphen
            "\u034f",  # Combining grapheme joiner
            "\u061c",  # Arabic letter mark
            "\u200b",  # Zero width space
            "\u200c",  # Zero width non-joiner
            "\u200d",  # Zero width joiner
            "\u200e",  # Left-to-right mark
            "\u200f",  # Right-to-left mark
        }

    @handle_validation_errors("unicode_validation")
    def validate(self, data: Any) -> bool:
        """Validate data for dangerous Unicode characters.

        Args:
            data: The data to validate

        Returns:
            bool: True if no dangerous Unicode characters detected, False otherwise

        """
        if isinstance(data, str):
            return self._validate_string(data)
        if isinstance(data, dict):
            return all(self.validate(value) for value in data.values())
        if isinstance(data, list):
            return all(self.validate(item) for item in data)
        return True

    def _validate_string(self, text: str) -> bool:
        """Validate a string for dangerous Unicode characters.

        Args:
            text: The string to validate

        Returns:
            bool: True if no dangerous Unicode characters detected, False otherwise

        """
        if not text:
            return True

        # Check for dangerous Unicode characters
        for char in self.dangerous_unicode:
            if char in text:
                self.logger.warning(
                    f"Dangerous Unicode character detected: U+{ord(char):04X}"
                )
                return False

        return True

    @handle_validation_errors("unicode_sanitization")
    def sanitize(self, data: Any) -> Any:
        """Sanitize data by removing dangerous Unicode characters.

        Args:
            data: The data to sanitize

        Returns:
            Any: The sanitized data

        """
        if isinstance(data, str):
            return self._sanitize_string(data)
        if isinstance(data, dict):
            return {key: self.sanitize(value) for key, value in data.items()}
        if isinstance(data, list):
            return [self.sanitize(item) for item in data]
        return data

    def _sanitize_string(self, text: str) -> str:
        """Sanitize a string by removing dangerous Unicode characters and normalizing.

        Args:
            text: The string to sanitize

        Returns:
            str: The sanitized string

        """
        if not text:
            return text

        # Remove dangerous Unicode characters
        for char in self.dangerous_unicode:
            text = text.replace(char, "")

        # Normalize Unicode to prevent bypass attempts
        text = unicodedata.normalize("NFKC", text)

        return text


class PayloadSizeValidator(InputValidator):
    """Payload size validator.

    Validates that JSON payloads don't exceed size limits.
    """

    def __init__(self, max_size: int = 10 * 1024 * 1024):  # 10MB default
        super().__init__()
        self.max_size = max_size

    def validate(self, data: Any) -> bool:
        """Validate that the data size doesn't exceed limits.

        Args:
            data: The data to validate

        Returns:
            bool: True if size is within limits, False otherwise

        """
        try:
            if isinstance(data, str):
                size = len(data.encode("utf-8"))
            else:
                # Convert to JSON to get size
                json_str = json.dumps(data, ensure_ascii=False)
                size = len(json_str.encode("utf-8"))

            if size > self.max_size:
                self.logger.warning(
                    f"Payload size {size} exceeds limit {self.max_size}"
                )
                return False

            return True
        except Exception as e:
            self.logger.error(f"Payload size validation error: {e}")
            return False


class JSONDepthValidator(InputValidator):
    """JSON depth validator.

    Validates that JSON structures don't exceed maximum nesting depth.
    """

    def __init__(self, max_depth: int = 100):
        super().__init__()
        self.max_depth = max_depth

    @handle_validation_errors("json_depth_validation")
    def validate(self, data: Any, depth: int = 0) -> bool:
        """Validate that the JSON depth doesn't exceed limits.

        Args:
            data: The data to validate
            depth: Current nesting depth

        Returns:
            bool: True if depth is within limits, False otherwise

        """
        if depth > self.max_depth:
            self.logger.warning(f"JSON depth {depth} exceeds limit {self.max_depth}")
            return False

        if isinstance(data, dict):
            return all(self.validate(value, depth + 1) for value in data.values())
        if isinstance(data, list):
            return all(self.validate(item, depth + 1) for item in data)
        return True


class SecurityValidator(InputValidator):
    """Comprehensive security validator that combines all security checks.
    Enhanced with unified security system integration.
    """

    def __init__(
        self,
        max_payload_size: int = 10 * 1024 * 1024,
        max_json_depth: int = 100,
        enable_xss_protection: bool = True,
        enable_unicode_validation: bool = True,
        use_unified_security: bool = True,  # Enable unified security integration
    ):
        super().__init__()

        # Check if unified security is actually available
        self.use_unified_security = use_unified_security and UNIFIED_SECURITY_AVAILABLE
        self.unified_security_validator = None

        # Initialize unified security validator if enabled and available
        if self.use_unified_security:
            self.unified_security_validator = get_unified_security_validator()
            logger.info("SecurityValidator: Unified security validator initialized")

        self.validators = []

        if enable_xss_protection:
            self.validators.append(XSSValidator())

        if enable_unicode_validation:
            self.validators.append(UnicodeValidator())

        self.validators.extend(
            [
                PayloadSizeValidator(max_payload_size),
                JSONDepthValidator(max_json_depth),
            ]
        )

    @handle_validation_errors("security_validation")
    def validate(
        self, data: Any, security_context: Optional["SecurityContext"] = None
    ) -> bool:
        """Run all security validations on the data.
        Enhanced with unified security validation when available.

        Args:
            data: The data to validate
            security_context: Optional security context for unified validation

        Returns:
            bool: True if all validations pass, False otherwise

        """
        # First, try unified security validation if available
        if self.use_unified_security and self.unified_security_validator:
            # Create a basic security context if none provided
            if security_context is None:
                security_context = SecurityContext(
                    user_id=None,
                    is_authenticated=False,
                    endpoint="validation",
                    method="VALIDATE",
                )

            # Use unified security validation
            validation_result = self.unified_security_validator.validate_comprehensive(
                data=data,
                context=security_context,
                level=SecurityLevel.STANDARD,
            )

            if not validation_result.is_valid():
                self.logger.warning(
                    f"Unified security validation failed: {validation_result.message}"
                )
                return False

            self.logger.debug("Unified security validation passed")

        # Run legacy validators as backup or primary validation
        for validator in self.validators:
            if not validator.validate(data):
                return False
        return True

    def sanitize(self, data: Any) -> Any:
        """Run security sanitization on the data.

        Uses a conservative approach that removes dangerous Unicode characters
        and HTML-encodes dangerous content rather than removing it completely.

        Args:
            data: The data to sanitize

        Returns:
            Any: The sanitized data

        """
        try:
            if isinstance(data, str):
                # First, remove dangerous Unicode characters
                for validator in self.validators:
                    if isinstance(validator, UnicodeValidator):
                        data = validator.sanitize(data)
                        break

                # Then HTML encode all content to neutralize XSS threats
                return html.escape(data, quote=True)
            elif isinstance(data, dict):
                return {key: self.sanitize(value) for key, value in data.items()}
            elif isinstance(data, list):
                return [self.sanitize(item) for item in data]
            else:
                return data
        except Exception as e:
            logger.error(f"Security sanitization error: {e}")
            return data


class ElectricalParametersValidator(InputValidator):
    """Validator for electrical parameters following IEEE/IEC/EN standards.

    Validates electrical design parameters including voltage, current, power,
    frequency, and other electrical characteristics for professional electrical
    design workflows.
    """

    def __init__(self):
        super().__init__()
        self.voltage_standards = {
            "low_voltage": {"min": 50, "max": 1000, "unit": "V"},
            "medium_voltage": {"min": 1000, "max": 35000, "unit": "V"},
            "high_voltage": {"min": 35000, "max": 800000, "unit": "V"},
        }
        self.frequency_standards = {
            "europe": {"nominal": 50, "tolerance": 0.5, "unit": "Hz"},
            "north_america": {"nominal": 60, "tolerance": 0.5, "unit": "Hz"},
        }
        self.cable_standards = {
            "iec": [
                "0.5",
                "0.75",
                "1.0",
                "1.5",
                "2.5",
                "4",
                "6",
                "10",
                "16",
                "25",
                "35",
                "50",
                "70",
                "95",
                "120",
                "150",
                "185",
                "240",
                "300",
                "400",
                "500",
                "630",
            ],
            "awg": [
                "18",
                "16",
                "14",
                "12",
                "10",
                "8",
                "6",
                "4",
                "2",
                "1",
                "1/0",
                "2/0",
                "3/0",
                "4/0",
            ],
        }

    @handle_validation_errors("electrical_parameters_validation")
    def validate(self, data: Any) -> bool:
        """Validate electrical parameters for professional electrical design.

        Args:
            data: Dictionary containing electrical parameters

        Returns:
            bool: True if validation passes, False otherwise
        """
        if not isinstance(data, dict):
            self.logger.error("Electrical parameters data must be a dictionary")
            return False

        try:
            # Validate voltage parameters
            if not self._validate_voltage_parameters(data):
                return False

            # Validate current parameters
            if not self._validate_current_parameters(data):
                return False

            # Validate power parameters
            if not self._validate_power_parameters(data):
                return False

            # Validate frequency parameters
            if not self._validate_frequency_parameters(data):
                return False

            # Validate power factor
            if not self._validate_power_factor(data):
                return False

            # Validate electrical consistency (P = V * I * PF)
            if not self._validate_electrical_consistency(data):
                return False

            # Validate cable specifications
            if not self._validate_cable_specifications(data):
                return False

            self.logger.debug("Electrical parameters validation passed")
            return True

        except Exception as e:
            self.logger.error(f"Electrical parameters validation error: {e}")
            return False

    def _validate_voltage_parameters(self, data: dict) -> bool:
        """Validate voltage parameters against standards."""
        voltage_fields = [
            "voltage",
            "rated_voltage",
            "rated_voltage",
            "supply_voltage_v",
        ]

        for field in voltage_fields:
            if field in data:
                voltage = data[field]
                if not isinstance(voltage, (int, float)) or voltage <= 0:
                    self.logger.error(f"Invalid voltage value: {voltage}")
                    return False

                # Check against voltage standards
                if voltage > 800000:  # Above high voltage range
                    self.logger.error(
                        f"Voltage {voltage}V exceeds maximum allowed (800kV)"
                    )
                    return False

                if voltage < 12:  # Below safe low voltage
                    self.logger.warning(
                        f"Voltage {voltage}V is below typical safe low voltage"
                    )

        return True

    def _validate_current_parameters(self, data: dict) -> bool:
        """Validate current parameters."""
        current_fields = [
            "current",
            "rated_current",
            "rated_current",
            "load_current_a",
        ]

        for field in current_fields:
            if field in data:
                current = data[field]
                if not isinstance(current, (int, float)) or current < 0:
                    self.logger.error(f"Invalid current value: {current}")
                    return False

                if current > 10000:  # Above typical industrial range
                    self.logger.warning(
                        f"Current {current}A is very high - verify design"
                    )

        return True

    def _validate_power_parameters(self, data: dict) -> bool:
        """Validate power parameters."""
        power_fields = ["power", "rated_power", "rated_power", "power_kw"]

        for field in power_fields:
            if field in data:
                power = data[field]
                if not isinstance(power, (int, float)) or power < 0:
                    self.logger.error(f"Invalid power value: {power}")
                    return False

                if power > 100000:  # Above typical industrial range (100MW)
                    self.logger.warning(f"Power {power}kW is very high - verify design")

        return True

    def _validate_frequency_parameters(self, data: dict) -> bool:
        """Validate frequency parameters against regional standards."""
        frequency_fields = ["frequency", "frequency_hz", "nominal_frequency"]

        for field in frequency_fields:
            if field in data:
                frequency = data[field]
                if not isinstance(frequency, (int, float)) or frequency <= 0:
                    self.logger.error(f"Invalid frequency value: {frequency}")
                    return False

                # Check against standard frequencies
                valid_frequencies = [
                    50,
                    60,
                    400,
                ]  # 50Hz (EU), 60Hz (NA), 400Hz (Aircraft)
                tolerance = 0.5

                if not any(
                    abs(frequency - std_freq) <= tolerance
                    for std_freq in valid_frequencies
                ):
                    self.logger.warning(
                        f"Frequency {frequency}Hz not standard (50/60/400Hz)"
                    )

        return True

    def _validate_power_factor(self, data: dict) -> bool:
        """Validate power factor parameters."""
        pf_fields = ["power_factor", "pf", "cos_phi"]

        for field in pf_fields:
            if field in data:
                pf = data[field]
                if not isinstance(pf, (int, float)) or pf <= 0 or pf > 1:
                    self.logger.error(
                        f"Invalid power factor: {pf} (must be 0 < PF <= 1)"
                    )
                    return False

                if pf < 0.7:
                    self.logger.warning(
                        f"Low power factor {pf} - consider power factor correction"
                    )

        return True

    def _validate_electrical_consistency(self, data: dict) -> bool:
        """Validate electrical parameter consistency (P = V * I * PF)."""
        # Extract electrical parameters
        voltage = (
            data.get("rated_voltage")
            or data.get("voltage")
            or data.get("supply_voltage_v")
        )
        current = (
            data.get("rated_current")
            or data.get("current")
            or data.get("load_current_a")
        )
        power = data.get("rated_power") or data.get("power_kw") or data.get("power")
        pf = data.get("power_factor") or data.get("pf") or 1.0

        if voltage and current and power:
            # Calculate expected power (P = V * I * PF / 1000 for kW)
            expected_power = (voltage * current * pf) / 1000
            tolerance = 0.15  # 15% tolerance for measurement/calculation variations

            if abs(power - expected_power) > (expected_power * tolerance):
                self.logger.error(
                    f"Electrical parameters inconsistent: P={power}kW, "
                    f"expected P={expected_power:.2f}kW (V={voltage}V, I={current}A, PF={pf})"
                )
                return False

        return True

    def _validate_cable_specifications(self, data: dict) -> bool:
        """Validate cable specifications against standards."""
        cable_fields = ["cable_size", "conductor_size", "cable_cross_section"]

        for field in cable_fields:
            if field in data:
                cable_size = str(data[field])

                # Check against IEC or AWG standards
                if (
                    cable_size not in self.cable_standards["iec"]
                    and cable_size not in self.cable_standards["awg"]
                ):
                    self.logger.warning(
                        f"Cable size {cable_size} not in standard IEC or AWG sizes"
                    )

        return True


class HeatTracingSpecificationValidator(InputValidator):
    """Validator for heat tracing specifications following IEC-62395 standards.

    Validates heat tracing design parameters including pipe specifications,
    insulation properties, environmental conditions, and cable specifications
    for professional heat tracing applications.
    """

    def __init__(self):
        super().__init__()
        self.pipe_materials = [
            "carbon_steel",
            "stainless_steel",
            "copper",
            "pvc",
            "hdpe",
            "fiberglass",
        ]
        self.insulation_types = [
            "mineral_wool",
            "polyurethane",
            "fiberglass",
            "cellular_glass",
            "perlite",
        ]
        self.cable_types = ["self_regulating", "series_resistance", "mineral_insulated"]
        self.hazardous_zones = ["zone_0", "zone_1", "zone_2", "non_hazardous"]

    @handle_validation_errors("heat_tracing_specification_validation")
    def validate(self, data: Any) -> bool:
        """Validate heat tracing specifications for professional design.

        Args:
            data: Dictionary containing heat tracing specifications

        Returns:
            bool: True if validation passes, False otherwise
        """
        if not isinstance(data, dict):
            self.logger.error("Heat tracing specification data must be a dictionary")
            return False

        try:
            # Validate pipe specifications
            if not self._validate_pipe_specifications(data):
                return False

            # Validate thermal parameters
            if not self._validate_thermal_parameters(data):
                return False

            # Validate environmental conditions
            if not self._validate_environmental_conditions(data):
                return False

            # Validate cable specifications
            if not self._validate_heat_tracing_cable_specs(data):
                return False

            # Validate safety requirements
            if not self._validate_safety_requirements(data):
                return False

            self.logger.debug("Heat tracing specifications validation passed")
            return True

        except Exception as e:
            self.logger.error(f"Heat tracing specifications validation error: {e}")
            return False

    def _validate_pipe_specifications(self, data: dict) -> bool:
        """Validate pipe specifications."""
        # Pipe diameter validation
        pipe_diameter_fields = ["pipe_diameter", "pipe_diameter_mm", "outer_diameter"]
        for field in pipe_diameter_fields:
            if field in data:
                diameter = data[field]
                if not isinstance(diameter, (int, float)) or diameter <= 0:
                    self.logger.error(f"Invalid pipe diameter: {diameter}")
                    return False
                if diameter > 2000:  # Above typical industrial range
                    self.logger.warning(f"Pipe diameter {diameter}mm is very large")

        # Pipe material validation
        if "pipe_material" in data:
            material = data["pipe_material"].lower()
            if material not in self.pipe_materials:
                self.logger.warning(
                    f"Pipe material {material} not in standard materials"
                )

        # Pipe length validation
        length_fields = ["pipe_length", "pipe_length_m", "length"]
        for field in length_fields:
            if field in data:
                length = data[field]
                if not isinstance(length, (int, float)) or length <= 0:
                    self.logger.error(f"Invalid pipe length: {length}")
                    return False
                if length > 10000:  # Above typical run length
                    self.logger.warning(
                        f"Pipe length {length}m is very long - consider circuit splitting"
                    )

        return True

    def _validate_thermal_parameters(self, data: dict) -> bool:
        """Validate thermal parameters."""
        # Temperature validation
        temp_fields = [
            "maintain_temperature",
            "process_temperature",
            "ambient_temperature",
        ]
        for field in temp_fields:
            if field in data:
                temp = data[field]
                if not isinstance(temp, (int, float)):
                    self.logger.error(f"Invalid temperature value: {temp}")
                    return False
                if field == "ambient_temperature" and (temp < -50 or temp > 60):
                    self.logger.warning(f"Ambient temperature {temp}°C is extreme")
                elif field in ["maintain_temperature", "process_temperature"] and (
                    temp < 0 or temp > 500
                ):
                    self.logger.warning(f"Process temperature {temp}°C is extreme")

        # Heat loss validation
        if "heat_loss" in data:
            heat_loss = data["heat_loss"]
            if not isinstance(heat_loss, (int, float)) or heat_loss < 0:
                self.logger.error(f"Invalid heat loss value: {heat_loss}")
                return False

        # Insulation thickness validation
        if "insulation_thickness" in data:
            thickness = data["insulation_thickness"]
            if not isinstance(thickness, (int, float)) or thickness < 0:
                self.logger.error(f"Invalid insulation thickness: {thickness}")
                return False
            if thickness > 200:  # Above typical range
                self.logger.warning(f"Insulation thickness {thickness}mm is very thick")

        return True

    def _validate_environmental_conditions(self, data: dict) -> bool:
        """Validate environmental conditions."""
        # Wind speed validation
        if "wind_speed" in data:
            wind_speed = data["wind_speed"]
            if not isinstance(wind_speed, (int, float)) or wind_speed < 0:
                self.logger.error(f"Invalid wind speed: {wind_speed}")
                return False
            if wind_speed > 50:  # Above hurricane force
                self.logger.warning(f"Wind speed {wind_speed}m/s is extreme")

        # Hazardous area validation
        if "hazardous_zone" in data:
            zone = data["hazardous_zone"].lower()
            if zone not in self.hazardous_zones:
                self.logger.error(f"Invalid hazardous zone: {zone}")
                return False

        return True

    def _validate_heat_tracing_cable_specs(self, data: dict) -> bool:
        """Validate heat tracing cable specifications."""
        # Cable type validation
        if "cable_type" in data:
            cable_type = data["cable_type"].lower()
            if cable_type not in self.cable_types:
                self.logger.warning(f"Cable type {cable_type} not in standard types")

        # Power output validation
        if "power_output" in data:
            power = data["power_output"]
            if not isinstance(power, (int, float)) or power < 0:
                self.logger.error(f"Invalid power output: {power}")
                return False
            if power > 100:  # Above typical range for W/m
                self.logger.warning(f"Power output {power}W/m is very high")

        # Voltage validation
        if "voltage" in data:
            voltage = data["voltage"]
            if not isinstance(voltage, (int, float)) or voltage <= 0:
                self.logger.error(f"Invalid voltage: {voltage}")
                return False
            # Standard heat tracing voltages
            standard_voltages = [120, 208, 230, 240, 277, 400, 480, 600]
            if voltage not in standard_voltages:
                self.logger.warning(
                    f"Voltage {voltage}V not in standard heat tracing voltages"
                )

        return True

    def _validate_safety_requirements(self, data: dict) -> bool:
        """Validate safety requirements."""
        # Temperature class validation for hazardous areas
        if "temperature_class" in data:
            temp_class = data["temperature_class"].upper()
            valid_classes = ["T1", "T2", "T3", "T4", "T5", "T6"]
            if temp_class not in valid_classes:
                self.logger.error(f"Invalid temperature class: {temp_class}")
                return False

        # IP rating validation
        if "ip_rating" in data:
            ip_rating = data["ip_rating"].upper()
            if not ip_rating.startswith("IP") or len(ip_rating) != 4:
                self.logger.error(f"Invalid IP rating format: {ip_rating}")
                return False

        return True


class StandardsComplianceValidator(InputValidator):
    """Validator for standards compliance data following IEEE/IEC/EN standards.

    Validates compliance data for electrical design standards including
    IEEE, IEC, and EN standards for professional electrical design workflows.
    """

    def __init__(self):
        super().__init__()
        self.ieee_standards = [
            "IEEE-80",
            "IEEE-142",
            "IEEE-242",
            "IEEE-399",
            "IEEE-519",
            "IEEE-551",
            "IEEE-739",
            "IEEE-946",
            "IEEE-1015",
            "IEEE-1100",
            "IEEE-1584",
        ]
        self.iec_standards = [
            "IEC-60038",
            "IEC-60364",
            "IEC-60439",
            "IEC-60529",
            "IEC-60664",
            "IEC-60947",
            "IEC-61000",
            "IEC-61439",
            "IEC-62271",
            "IEC-62395",
        ]
        self.en_standards = [
            "EN-50110",
            "EN-50160",
            "EN-50522",
            "EN-60038",
            "EN-60204",
            "EN-60439",
            "EN-60529",
            "EN-60947",
            "EN-61000",
            "EN-61439",
        ]
        self.compliance_levels = ["basic", "standard", "enhanced", "full"]

    @handle_validation_errors("standards_compliance_validation")
    def validate(self, data: Any) -> bool:
        """Validate standards compliance data for professional electrical design.

        Args:
            data: Dictionary containing standards compliance data

        Returns:
            bool: True if validation passes, False otherwise
        """
        if not isinstance(data, dict):
            self.logger.error("Standards compliance data must be a dictionary")
            return False

        try:
            # Validate applicable standards
            if not self._validate_applicable_standards(data):
                return False

            # Validate compliance level
            if not self._validate_compliance_level(data):
                return False

            # Validate compliance status
            if not self._validate_compliance_status(data):
                return False

            # Validate design parameters
            if not self._validate_design_parameters(data):
                return False

            # Validate certification requirements
            if not self._validate_certification_requirements(data):
                return False

            self.logger.debug("Standards compliance validation passed")
            return True

        except Exception as e:
            self.logger.error(f"Standards compliance validation error: {e}")
            return False

    def _validate_applicable_standards(self, data: dict) -> bool:
        """Validate applicable standards list."""
        standards_fields = ["applicable_standards", "project_standards", "standards"]

        for field in standards_fields:
            if field in data:
                standards = data[field]
                if not isinstance(standards, list):
                    self.logger.error(f"Standards field {field} must be a list")
                    return False

                for standard in standards:
                    if not isinstance(standard, str):
                        self.logger.error(
                            f"Standard identifier must be string: {standard}"
                        )
                        return False

                    # Check if standard is recognized
                    standard_upper = standard.upper()
                    if not any(
                        standard_upper in std_list
                        for std_list in [
                            [s.upper() for s in self.ieee_standards],
                            [s.upper() for s in self.iec_standards],
                            [s.upper() for s in self.en_standards],
                        ]
                    ):
                        self.logger.warning(
                            f"Standard {standard} not in recognized IEEE/IEC/EN standards"
                        )

        return True

    def _validate_compliance_level(self, data: dict) -> bool:
        """Validate compliance level."""
        if "compliance_level" in data:
            level = data["compliance_level"].lower()
            if level not in self.compliance_levels:
                self.logger.error(f"Invalid compliance level: {level}")
                return False

        return True

    def _validate_compliance_status(self, data: dict) -> bool:
        """Validate compliance status data."""
        if "compliance_status" in data:
            status = data["compliance_status"]
            if not isinstance(status, dict):
                self.logger.error("Compliance status must be a dictionary")
                return False

            for standard, compliant in status.items():
                if not isinstance(standard, str):
                    self.logger.error(f"Standard identifier must be string: {standard}")
                    return False

                if not isinstance(compliant, bool):
                    self.logger.error(
                        f"Compliance status must be boolean for {standard}"
                    )
                    return False

        return True

    def _validate_design_parameters(self, data: dict) -> bool:
        """Validate design parameters for standards compliance."""
        if "design_parameters" in data:
            params = data["design_parameters"]
            if not isinstance(params, dict):
                self.logger.error("Design parameters must be a dictionary")
                return False

            # Validate hazardous area parameters
            if "hazardous_area_zone" in params:
                zone = params["hazardous_area_zone"]
                valid_zones = ["zone_0", "zone_1", "zone_2", "non_hazardous"]
                if zone not in valid_zones:
                    self.logger.error(f"Invalid hazardous area zone: {zone}")
                    return False

            # Validate gas group
            if "gas_group" in params:
                gas_group = params["gas_group"].upper()
                valid_groups = ["IIA", "IIB", "IIC"]
                if gas_group not in valid_groups:
                    self.logger.error(f"Invalid gas group: {gas_group}")
                    return False

            # Validate temperature class
            if "temperature_class" in params:
                temp_class = params["temperature_class"].upper()
                valid_classes = ["T1", "T2", "T3", "T4", "T5", "T6"]
                if temp_class not in valid_classes:
                    self.logger.error(f"Invalid temperature class: {temp_class}")
                    return False

        return True

    def _validate_certification_requirements(self, data: dict) -> bool:
        """Validate certification requirements."""
        # CE marking validation
        if "ce_marking" in data:
            ce_marking = data["ce_marking"]
            if not isinstance(ce_marking, bool):
                self.logger.error("CE marking must be boolean")
                return False

        # ATEX certification validation
        if "atex_certified" in data:
            atex = data["atex_certified"]
            if not isinstance(atex, bool):
                self.logger.error("ATEX certification must be boolean")
                return False

        # IECEx certification validation
        if "iecex_certified" in data:
            iecex = data["iecex_certified"]
            if not isinstance(iecex, bool):
                self.logger.error("IECEx certification must be boolean")
                return False

        return True


# Global instances for easy access
security_validator = SecurityValidator()
electrical_validator = ElectricalParametersValidator()
heat_tracing_validator = HeatTracingSpecificationValidator()
standards_compliance_validator = StandardsComplianceValidator()
