

"""Create user and project tables - basic version

Revision ID: 6f9f7192828a
Revises: 255d7283e9af
Create Date: 2025-07-08 16:45:28.478088

"""
from alembic import op
import sqlalchemy as sa
from src.core.utils.json_validation import FlexibleJSON


# revision identifiers, used by Alembic.
revision = '6f9f7192828a'
down_revision = '255d7283e9af'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('User',
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('password_hash', sa.String(), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('role', sa.String(length=50), nullable=False),
    sa.Column('is_admin', sa.<PERSON>(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('name', name='uq_user_name')
    )
    op.create_table('UserPreference',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('ui_theme', sa.String(), nullable=False),
    sa.Column('default_min_ambient_temp_c', sa.Float(), nullable=True),
    sa.Column('default_max_ambient_temp_c', sa.Float(), nullable=True),
    sa.Column('default_desired_maintenance_temp_c', sa.Float(), nullable=True),
    sa.Column('default_safety_margin_percent', sa.Float(), nullable=False),
    sa.Column('preferred_cable_manufacturers_json', FlexibleJSON(), nullable=True),
    sa.Column('preferred_control_device_manufacturers_json', FlexibleJSON(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_by_user_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['deleted_by_user_id'], ['User.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['User.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('projects',
    sa.Column('project_number', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('designer', sa.String(), nullable=True),
    sa.Column('client', sa.String(), nullable=True),
    sa.Column('location', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('min_ambient_temp_c', sa.Float(), nullable=False),
    sa.Column('max_ambient_temp_c', sa.Float(), nullable=False),
    sa.Column('desired_maintenance_temp_c', sa.Float(), nullable=False),
    sa.Column('wind_speed_ms', sa.Float(), nullable=True),
    sa.Column('installation_environment', src.core.models.base.EnumType(), nullable=True),
    sa.Column('available_voltages_json', sa.Text(), nullable=True),
    sa.Column('default_cable_manufacturer', sa.String(), nullable=True),
    sa.Column('default_control_device_manufacturer', sa.String(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('notes', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_by_user_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['deleted_by_user_id'], ['User.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', name='uq_project_name'),
    sa.UniqueConstraint('project_number', name='uq_project_number')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('projects')
    op.drop_table('UserPreference')
    op.drop_table('User')
    # ### end Alembic commands ###
