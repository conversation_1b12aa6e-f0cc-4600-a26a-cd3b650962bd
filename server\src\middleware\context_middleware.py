# src/middleware/context_middleware.py
"""Context Middleware for Ultimate Electrical Designer Backend.

This module provides request-scoped context management including:
- Request ID generation and injection
- User context attachment
- Locale context determination
- Request timing and metadata
"""

import time
import uuid
from contextvars import ContextVar
from typing import Any

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from src.config.logging_config import logger

# Context variables for request-scoped data
request_id_var: ContextVar[str] = ContextVar("request_id", default="")
user_context_var: ContextVar[dict[str, Any]] = ContextVar("user_context", default={})
locale_context_var: ContextVar[str] = ContextVar("locale", default="en")
request_start_time_var: ContextVar[float] = ContextVar(
    "request_start_time", default=0.0
)


class ContextMiddleware(BaseHTTPMiddleware):
    """Context middleware for managing request-scoped contextual data.

    Features:
    - Generates unique request IDs for tracing
    - Manages user context from authentication
    - Determines locale from headers
    - Tracks request timing
    - Provides context variables for access throughout the request lifecycle
    """

    def __init__(
        self,
        app: ASGIApp,
        enable_request_id: bool = True,
        enable_user_context: bool = True,
        enable_locale_detection: bool = True,
        enable_timing: bool = True,
        default_locale: str = "en",
    ):
        super().__init__(app)
        self.enable_request_id = enable_request_id
        self.enable_user_context = enable_user_context
        self.enable_locale_detection = enable_locale_detection
        self.enable_timing = enable_timing
        self.default_locale = default_locale

    async def dispatch(self, request: Request, call_next) -> Response:
        """Main middleware dispatch method that sets up request context."""
        try:
            # 1. Generate and set request ID
            if self.enable_request_id:
                request_id = self._generate_request_id()
                request_id_var.set(request_id)
                # Add to request state for access in route handlers
                request.state.request_id = request_id
                logger.debug(f"Request ID generated: {request_id}")

            # 2. Set request start time
            if self.enable_timing:
                start_time = time.time()
                request_start_time_var.set(start_time)
                request.state.start_time = start_time

            # 3. Determine and set locale
            if self.enable_locale_detection:
                locale = self._determine_locale(request)
                locale_context_var.set(locale)
                request.state.locale = locale
                logger.debug(f"Locale determined: {locale}")

            # 4. Extract and set user context (if available)
            if self.enable_user_context:
                user_context = await self._extract_user_context(request)
                user_context_var.set(user_context)
                request.state.user_context = user_context
                if user_context:
                    logger.debug(
                        f"User context set: {user_context.get('id', 'unknown')}"
                    )

            # Process the request
            response = await call_next(request)

            # 5. Add context headers to response
            self._add_context_headers(response, request)

            # 6. Log request completion
            if self.enable_timing:
                duration = time.time() - request_start_time_var.get(0.0)
                logger.info(
                    f"Request completed - ID: {request_id_var.get('unknown')} "
                    f"Duration: {duration:.3f}s Status: {response.status_code}"
                )

            return response

        except Exception as e:
            logger.error("Context middleware error: {}", str(e), exc_info=True)
            # Continue processing even if context setup fails
            return await call_next(request)

    def _generate_request_id(self) -> str:
        """Generate a unique request ID."""
        return str(uuid.uuid4())

    def _determine_locale(self, request: Request) -> str:
        """Determine the locale from request headers.

        Priority:
        1. Accept-Language header
        2. X-Locale header
        3. Default locale
        """
        # Check X-Locale header first (explicit preference)
        locale_header = request.headers.get("x-locale")
        if locale_header:
            return locale_header.lower()

        # Check Accept-Language header
        accept_language = request.headers.get("accept-language")
        if accept_language:
            # Parse Accept-Language header (simplified)
            # Format: "en-US,en;q=0.9,fr;q=0.8"
            languages = accept_language.split(",")
            if languages:
                primary_lang = languages[0].split(";")[0].strip()
                # Extract language code (e.g., "en" from "en-US")
                lang_code = primary_lang.split("-")[0].lower()
                return lang_code

        return self.default_locale

    async def _extract_user_context(self, request: Request) -> dict[str, Any]:
        """Extract user context from the request.

        This looks for user information from:
        1. Request state (set by authentication middleware)
        2. Authorization header (basic parsing)
        3. Session data
        """
        user_context = {}

        # Check if user is already set in request state (by auth middleware)
        if hasattr(request.state, "user") and request.state.user:
            user_context = {
                "id": getattr(request.state.user, "id", None),
                "username": getattr(request.state.user, "username", None),
                "email": getattr(request.state.user, "email", None),
                "is_admin": getattr(request.state.user, "is_admin", False),
                "roles": getattr(request.state.user, "roles", []),
            }

        # If no user in state, try to extract from headers (basic)
        elif "authorization" in request.headers:
            auth_header = request.headers["authorization"]
            if auth_header.startswith("Bearer "):
                # In a real implementation, you'd decode the JWT here
                # For now, just indicate that there's an authenticated user
                user_context = {
                    "id": "authenticated_user",
                    "authenticated": True,
                    "token_present": True,
                }

        return user_context

    def _add_context_headers(self, response: Response, request: Request) -> None:
        """Add context information to response headers."""
        # Add request ID to response for tracing
        if self.enable_request_id and hasattr(request.state, "request_id"):
            response.headers["X-Request-ID"] = request.state.request_id

        # Add processing time
        if self.enable_timing and hasattr(request.state, "start_time"):
            duration = time.time() - request.state.start_time
            response.headers["X-Response-Time"] = f"{duration:.3f}s"

        # Add locale information
        if self.enable_locale_detection and hasattr(request.state, "locale"):
            response.headers["X-Content-Language"] = request.state.locale


# Utility functions for accessing context variables
def get_request_id() -> str:
    """Get the current request ID."""
    return request_id_var.get("")


def get_user_context() -> dict[str, Any]:
    """Get the current user context."""
    return user_context_var.get({})


def get_locale() -> str:
    """Get the current locale."""
    return locale_context_var.get("en")


def get_request_start_time() -> float:
    """Get the request start time."""
    return request_start_time_var.get(0.0)


def get_request_duration() -> float:
    """Get the current request duration."""
    start_time = get_request_start_time()
    if start_time > 0:
        return time.time() - start_time
    return 0.0
