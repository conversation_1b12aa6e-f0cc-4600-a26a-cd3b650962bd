# tests/security/test_input_validation/test_input_validators.py
"""
Comprehensive tests for input validation modules.

This module tests all input validation functionality including XSS protection,
Unicode validation, payload size validation, JSON depth validation, and
unified security integration.
"""

import json
import os
import sys
from unittest.mock import Mock, patch

import pytest

# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
sys.path.insert(0, server_path)

from src.core.security.input_validators import (
    ValidationError,
    InputValidator,
    XSSValidator,
    UnicodeValidator,
    PayloadSizeValidator,
    JSONDepthValidator,
    SecurityValidator,
)

pytestmark = [pytest.mark.unit, pytest.mark.security]


class TestValidationError:
    """Test suite for ValidationError exception."""

    def test_validation_error_creation(self):
        """Test ValidationError can be created and raised."""
        error = ValidationError("Test validation error")
        assert str(error) == "Test validation error"

        with pytest.raises(ValidationError, match="Test validation error"):
            raise error

    def test_validation_error_inheritance(self):
        """Test ValidationError inherits from Exception."""
        error = ValidationError("Test error")
        assert isinstance(error, Exception)


class TestInputValidator:
    """Test suite for base InputValidator class."""

    def test_input_validator_initialization(self):
        """Test InputValidator initialization."""
        validator = InputValidator()

        assert validator.logger is not None
        assert hasattr(validator, "validate")
        assert hasattr(validator, "sanitize")

    def test_input_validator_validate_not_implemented(self):
        """Test that base validate method raises NotImplementedError."""
        validator = InputValidator()

        with pytest.raises(NotImplementedError):
            validator.validate("test data")

    def test_input_validator_sanitize_default(self):
        """Test that base sanitize method returns data unchanged."""
        validator = InputValidator()
        test_data = {"test": "data"}

        result = validator.sanitize(test_data)
        assert result == test_data
        assert result is test_data  # Should return same object


class TestXSSValidator:
    """Test suite for XSSValidator class."""

    def test_xss_validator_initialization(self):
        """Test XSSValidator initialization."""
        validator = XSSValidator()

        assert validator.xss_patterns is not None
        assert len(validator.xss_patterns) > 0
        assert validator.dangerous_attributes is not None
        assert len(validator.dangerous_attributes) > 0

    def test_xss_validator_patterns_compilation(self):
        """Test that XSS patterns are properly compiled."""
        validator = XSSValidator()

        # All patterns should be compiled regex objects
        for pattern in validator.xss_patterns:
            assert hasattr(pattern, "search")
            assert hasattr(pattern, "pattern")

    def test_xss_validator_safe_strings(self):
        """Test XSSValidator with safe strings."""
        validator = XSSValidator()

        safe_strings = [
            "Hello world",
            "This is a normal string",
            "Email: <EMAIL>",
            "Phone: ******-567-8900",
            "Price: $19.99",
            "",  # Empty string
            "Unicode: café résumé naïve",
        ]

        for safe_string in safe_strings:
            assert validator.validate(safe_string) is True, (
                f"Safe string failed: {safe_string}"
            )

    def test_xss_validator_dangerous_strings(self):
        """Test XSSValidator with dangerous XSS strings."""
        validator = XSSValidator()

        dangerous_strings = [
            "<script>alert('xss')</script>",
            "<img src=x onerror=alert('xss')>",
            "javascript:alert('xss')",
            "<iframe src='javascript:alert(1)'></iframe>",
            "<svg onload=alert('xss')>",
            "<div onclick='alert(1)'>Click me</div>",
            "<object data='javascript:alert(1)'></object>",
            "<embed src='javascript:alert(1)'>",
            "<style>body{background:url('javascript:alert(1)')}</style>",
            "<link rel='stylesheet' href='javascript:alert(1)'>",
        ]

        for dangerous_string in dangerous_strings:
            assert validator.validate(dangerous_string) is False, (
                f"Dangerous string passed: {dangerous_string}"
            )

    def test_xss_validator_dangerous_attributes(self):
        """Test XSSValidator with dangerous attributes."""
        validator = XSSValidator()

        dangerous_inputs = [
            "onclick='alert(1)'",
            "onmouseover='alert(1)'",
            "onerror='alert(1)'",
            "onload='alert(1)'",
            "onfocus='alert(1)'",
            "onblur='alert(1)'",
            "onchange='alert(1)'",
            "onsubmit='alert(1)'",
        ]

        for dangerous_input in dangerous_inputs:
            assert validator.validate(dangerous_input) is False, (
                f"Dangerous attribute passed: {dangerous_input}"
            )

    def test_xss_validator_nested_data_structures(self):
        """Test XSSValidator with nested data structures."""
        validator = XSSValidator()

        # Safe nested data
        safe_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "details": {"age": 30, "hobbies": ["reading", "swimming", "coding"]},
        }
        assert validator.validate(safe_data) is True

        # Dangerous nested data
        dangerous_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "bio": "<script>alert('xss')</script>",
            "details": {"age": 30, "hobbies": ["reading", "swimming"]},
        }
        assert validator.validate(dangerous_data) is False

    def test_xss_validator_list_data(self):
        """Test XSSValidator with list data."""
        validator = XSSValidator()

        # Safe list
        safe_list = ["item1", "item2", "item3"]
        assert validator.validate(safe_list) is True

        # Dangerous list
        dangerous_list = ["item1", "<script>alert('xss')</script>", "item3"]
        assert validator.validate(dangerous_list) is False

    def test_xss_validator_non_string_data(self):
        """Test XSSValidator with non-string data."""
        validator = XSSValidator()

        non_string_data = [
            123,
            45.67,
            True,
            False,
            None,
            {"number": 42},
            [1, 2, 3],
        ]

        for data in non_string_data:
            assert validator.validate(data) is True, f"Non-string data failed: {data}"

    def test_xss_validator_sanitize_safe_strings(self):
        """Test XSSValidator sanitization with safe strings."""
        validator = XSSValidator()

        safe_strings = [
            "Hello world",
            "This is normal text",
            "Email: <EMAIL>",
        ]

        for safe_string in safe_strings:
            result = validator.sanitize(safe_string)
            assert result == safe_string

    def test_xss_validator_sanitize_dangerous_strings(self):
        """Test XSSValidator sanitization with dangerous strings."""
        validator = XSSValidator()

        test_cases = [
            (
                "<script>alert('xss')</script>",
                "",  # Script tags are completely removed
            ),
            (
                "<img src=x onerror=alert('xss')>",
                "&lt;img src=x alert(&#x27;xss&#x27;)&gt;",  # onerror= removed, rest escaped
            ),
            (
                "javascript:alert('xss')",
                "alert(&#x27;xss&#x27;)",  # javascript: removed, rest escaped
            ),
            ("<div>Normal content</div>", "&lt;div&gt;Normal content&lt;/div&gt;"),
            (
                "Hello <script>bad</script> world",
                "Hello  world",
            ),  # Script removed, text preserved
        ]

        for dangerous_input, expected_output in test_cases:
            result = validator.sanitize(dangerous_input)
            assert result == expected_output, (
                f"Sanitization failed for: {dangerous_input}"
            )

    def test_xss_validator_sanitize_nested_data(self):
        """Test XSSValidator sanitization with nested data."""
        validator = XSSValidator()

        input_data = {
            "name": "John Doe",
            "bio": "<script>alert('xss')</script>",
            "details": {
                "description": "<img src=x onerror=alert('xss')>",
                "hobbies": ["reading", "<script>alert('hobby')</script>"],
            },
        }

        result = validator.sanitize(input_data)

        assert result["name"] == "John Doe"
        assert result["bio"] == ""  # Script tag completely removed
        assert (
            result["details"]["description"]
            == "&lt;img src=x alert(&#x27;xss&#x27;)&gt;"
        )  # onerror= removed, rest escaped
        assert result["details"]["hobbies"][0] == "reading"
        assert result["details"]["hobbies"][1] == ""  # Script tag completely removed

    def test_xss_validator_error_handling(self):
        """Test XSSValidator error handling."""
        validator = XSSValidator()

        # Mock logger to capture error logs
        with patch.object(validator, "logger") as mock_logger:
            # Test with data that might cause validation errors
            # This should not raise an exception but return False
            result = validator.validate(None)
            assert result is True  # None is considered safe

            # Test with complex circular reference (if it causes issues)
            circular_data = {}
            circular_data["self"] = circular_data

            # This might cause recursion issues, should be handled gracefully
            try:
                result = validator.validate(circular_data)
                # Should either return True/False, not raise exception
                assert isinstance(result, bool)
            except RecursionError:
                # If recursion error occurs, it should be caught and logged
                pass

    @patch("src.config.logging_config.logger")
    def test_xss_validator_logging(self, mock_logger):
        """Test XSSValidator logging functionality."""
        validator = XSSValidator()
        validator.logger = mock_logger

        # Test dangerous input that should trigger warning
        dangerous_input = "<script>alert('xss')</script>"
        validator.validate(dangerous_input)

        # Should have logged a warning
        mock_logger.warning.assert_called()
        warning_call = mock_logger.warning.call_args[0][0]
        assert "Detected XSS pattern" in warning_call


class TestUnicodeValidator:
    """Test suite for UnicodeValidator class."""

    def test_unicode_validator_initialization(self):
        """Test UnicodeValidator initialization."""
        validator = UnicodeValidator()

        assert validator.dangerous_unicode is not None
        assert len(validator.dangerous_unicode) > 0
        assert isinstance(validator.dangerous_unicode, set)

    def test_unicode_validator_dangerous_characters(self):
        """Test that dangerous Unicode characters are properly defined."""
        validator = UnicodeValidator()

        expected_dangerous = {
            "\u0000",  # Null byte
            "\u202e",  # Right-to-left override
            "\ufeff",  # Byte order mark
            "\u2028",  # Line separator
            "\u2029",  # Paragraph separator
            "\u00ad",  # Soft hyphen
            "\u034f",  # Combining grapheme joiner
            "\u061c",  # Arabic letter mark
            "\u200b",  # Zero width space
            "\u200c",  # Zero width non-joiner
            "\u200d",  # Zero width joiner
            "\u200e",  # Left-to-right mark
            "\u200f",  # Right-to-left mark
        }

        for char in expected_dangerous:
            assert char in validator.dangerous_unicode

    def test_unicode_validator_safe_strings(self):
        """Test UnicodeValidator with safe Unicode strings."""
        validator = UnicodeValidator()

        safe_strings = [
            "Hello world",
            "café résumé naïve",
            "こんにちは世界",  # Japanese
            "مرحبا بالعالم",  # Arabic
            "Здравствуй мир",  # Russian
            "🌍🌎🌏",  # Emojis
            "αβγδε",  # Greek
            "ñáéíóú",  # Spanish accents
            "",  # Empty string
        ]

        for safe_string in safe_strings:
            assert validator.validate(safe_string) is True, (
                f"Safe Unicode string failed: {repr(safe_string)}"
            )

    def test_unicode_validator_dangerous_strings(self):
        """Test UnicodeValidator with dangerous Unicode strings."""
        validator = UnicodeValidator()

        dangerous_strings = [
            "Hello\u0000world",  # Null byte
            "Text\u202eOverride",  # Right-to-left override
            "\ufeffBOM text",  # Byte order mark
            "Line\u2028separator",  # Line separator
            "Para\u2029separator",  # Paragraph separator
            "Soft\u00adhyphen",  # Soft hyphen
            "Zero\u200bwidth\u200cspace",  # Zero width characters
            "Direction\u200e\u200fmarks",  # Direction marks
        ]

        for dangerous_string in dangerous_strings:
            assert validator.validate(dangerous_string) is False, (
                f"Dangerous Unicode string passed: {repr(dangerous_string)}"
            )

    def test_unicode_validator_nested_data(self):
        """Test UnicodeValidator with nested data structures."""
        validator = UnicodeValidator()

        # Safe nested data
        safe_data = {
            "name": "José María",
            "description": "café résumé",
            "tags": ["español", "français", "português"],
        }
        assert validator.validate(safe_data) is True

        # Dangerous nested data
        dangerous_data = {
            "name": "José María",
            "description": "Text with\u0000null byte",
            "tags": ["safe", "also\u202edangerous"],
        }
        assert validator.validate(dangerous_data) is False

    def test_unicode_validator_sanitize_safe_strings(self):
        """Test UnicodeValidator sanitization with safe strings."""
        validator = UnicodeValidator()

        safe_strings = [
            "Hello world",
            "café résumé naïve",
            "こんにちは世界",
        ]

        for safe_string in safe_strings:
            result = validator.sanitize(safe_string)
            assert result == safe_string

    def test_unicode_validator_sanitize_dangerous_strings(self):
        """Test UnicodeValidator sanitization with dangerous strings."""
        validator = UnicodeValidator()

        test_cases = [
            ("Hello\u0000world", "Helloworld"),
            ("Text\u202eOverride", "TextOverride"),
            ("\ufeffBOM text", "BOM text"),
            ("Zero\u200bwidth\u200cspace", "Zerowidthspace"),
            ("Multiple\u0000\u202e\u200bdangerous", "Multipledangerous"),
        ]

        for dangerous_input, expected_output in test_cases:
            result = validator.sanitize(dangerous_input)
            assert result == expected_output, (
                f"Sanitization failed for: {repr(dangerous_input)}"
            )

    def test_unicode_validator_sanitize_normalization(self):
        """Test UnicodeValidator Unicode normalization."""
        validator = UnicodeValidator()

        # Test Unicode normalization (NFKC)
        test_cases = [
            ("café", "café"),  # Should normalize to NFC form
            ("ﬁle", "file"),  # Ligature should be decomposed
        ]

        for input_text, expected_pattern in test_cases:
            result = validator.sanitize(input_text)
            # The exact normalization result may vary, but should be consistent
            assert isinstance(result, str)
            assert len(result) > 0

    def test_unicode_validator_error_handling(self):
        """Test UnicodeValidator error handling."""
        validator = UnicodeValidator()

        # Test with None
        assert validator.validate(None) is True

        # Test with non-string types
        assert validator.validate(123) is True
        assert validator.validate([]) is True
        assert validator.validate({}) is True

    @patch("src.core.security.input_validators.logger")
    def test_unicode_validator_logging(self, mock_logger):
        """Test UnicodeValidator logging functionality."""
        validator = UnicodeValidator()
        validator.logger = mock_logger

        # Test dangerous input that should trigger warning
        dangerous_input = "Text with\u0000null byte"
        validator.validate(dangerous_input)

        # Should have logged a warning
        mock_logger.warning.assert_called()
        warning_call = mock_logger.warning.call_args[0][0]
        assert "Dangerous Unicode character detected" in warning_call


class TestPayloadSizeValidator:
    """Test suite for PayloadSizeValidator class."""

    def test_payload_size_validator_initialization_default(self):
        """Test PayloadSizeValidator initialization with default size."""
        validator = PayloadSizeValidator()

        assert validator.max_size == 10 * 1024 * 1024  # 10MB default

    def test_payload_size_validator_initialization_custom(self):
        """Test PayloadSizeValidator initialization with custom size."""
        custom_size = 5 * 1024 * 1024  # 5MB
        validator = PayloadSizeValidator(max_size=custom_size)

        assert validator.max_size == custom_size

    def test_payload_size_validator_small_strings(self):
        """Test PayloadSizeValidator with small strings."""
        validator = PayloadSizeValidator(max_size=1024)  # 1KB limit

        small_strings = [
            "Hello world",
            "A" * 100,
            "Unicode: café résumé",
            "",
        ]

        for small_string in small_strings:
            assert validator.validate(small_string) is True, (
                f"Small string failed: {len(small_string)} chars"
            )

    def test_payload_size_validator_large_strings(self):
        """Test PayloadSizeValidator with large strings."""
        validator = PayloadSizeValidator(max_size=1024)  # 1KB limit

        # Create strings larger than 1KB
        large_strings = [
            "A" * 2000,  # 2KB
            "Unicode: " + "café résumé " * 100,  # Large Unicode string
        ]

        for large_string in large_strings:
            assert validator.validate(large_string) is False, (
                f"Large string passed: {len(large_string)} chars"
            )

    def test_payload_size_validator_json_data(self):
        """Test PayloadSizeValidator with JSON data."""
        validator = PayloadSizeValidator(max_size=1024)  # 1KB limit

        # Small JSON data
        small_data = {"name": "John", "age": 30, "city": "New York"}
        assert validator.validate(small_data) is True

        # Large JSON data
        large_data = {"data": "A" * 2000}  # Will be larger than 1KB when serialized
        assert validator.validate(large_data) is False

    def test_payload_size_validator_complex_data(self):
        """Test PayloadSizeValidator with complex nested data."""
        validator = PayloadSizeValidator(max_size=2048)  # 2KB limit

        # Complex but small data
        small_complex_data = {
            "users": [
                {"name": "John", "age": 30},
                {"name": "Jane", "age": 25},
            ],
            "metadata": {"version": "1.0", "created": "2025-01-01"},
        }
        assert validator.validate(small_complex_data) is True

        # Complex and large data
        large_complex_data = {
            "users": [{"name": f"User{i}", "data": "A" * 100} for i in range(50)],
            "large_field": "B" * 1000,
        }
        assert validator.validate(large_complex_data) is False

    def test_payload_size_validator_unicode_encoding(self):
        """Test PayloadSizeValidator with Unicode encoding considerations."""
        validator = PayloadSizeValidator(max_size=100)  # Small limit

        # ASCII characters (1 byte each)
        ascii_string = "A" * 50
        assert validator.validate(ascii_string) is True

        # Unicode characters (multiple bytes each)
        unicode_string = "🌍" * 30  # Emojis are 4 bytes each in UTF-8
        # 30 * 4 = 120 bytes, should exceed 100 byte limit
        assert validator.validate(unicode_string) is False

    def test_payload_size_validator_error_handling(self):
        """Test PayloadSizeValidator error handling."""
        validator = PayloadSizeValidator()

        # Test with None
        assert validator.validate(None) is True  # None serializes to "null"

        # Test with non-serializable data (should handle gracefully)
        class NonSerializable:
            pass

        non_serializable = NonSerializable()
        # This should return False due to JSON serialization error
        assert validator.validate(non_serializable) is False

    @patch("src.core.security.input_validators.logger")
    def test_payload_size_validator_logging(self, mock_logger):
        """Test PayloadSizeValidator logging functionality."""
        validator = PayloadSizeValidator(max_size=100)
        validator.logger = mock_logger

        # Test large payload that should trigger warning
        large_payload = "A" * 200
        validator.validate(large_payload)

        # Should have logged a warning about size limit
        mock_logger.warning.assert_called()
        warning_call = mock_logger.warning.call_args[0][0]
        assert "Payload size" in warning_call and "exceeds limit" in warning_call


class TestJSONDepthValidator:
    """Test suite for JSONDepthValidator class."""

    def test_json_depth_validator_initialization_default(self):
        """Test JSONDepthValidator initialization with default depth."""
        validator = JSONDepthValidator()

        assert validator.max_depth == 100  # Default depth

    def test_json_depth_validator_initialization_custom(self):
        """Test JSONDepthValidator initialization with custom depth."""
        custom_depth = 50
        validator = JSONDepthValidator(max_depth=custom_depth)

        assert validator.max_depth == custom_depth

    def test_json_depth_validator_shallow_data(self):
        """Test JSONDepthValidator with shallow data structures."""
        validator = JSONDepthValidator(max_depth=5)

        shallow_data = [
            "simple string",
            123,
            {"key": "value"},
            ["item1", "item2", "item3"],
            {"level1": {"level2": "value"}},
        ]

        for data in shallow_data:
            assert validator.validate(data) is True, f"Shallow data failed: {data}"

    def test_json_depth_validator_deep_data(self):
        """Test JSONDepthValidator with deep nested data."""
        validator = JSONDepthValidator(max_depth=3)

        # Create deeply nested data that exceeds max_depth
        deep_data = {"level1": {"level2": {"level3": {"level4": "too deep"}}}}

        assert validator.validate(deep_data) is False

    def test_json_depth_validator_complex_nested_structures(self):
        """Test JSONDepthValidator with complex nested structures."""
        validator = JSONDepthValidator(max_depth=5)

        # Complex but within depth limit
        valid_complex_data = {
            "users": [
                {
                    "name": "John",
                    "details": {
                        "age": 30,
                        "address": {"city": "New York", "country": "USA"},
                    },
                }
            ]
        }
        assert validator.validate(valid_complex_data) is True

        # Complex and exceeding depth limit
        invalid_complex_data = {
            "level1": {
                "level2": {"level3": {"level4": {"level5": {"level6": "too deep"}}}}
            }
        }
        assert validator.validate(invalid_complex_data) is False

    def test_json_depth_validator_list_nesting(self):
        """Test JSONDepthValidator with nested lists."""
        validator = JSONDepthValidator(max_depth=3)

        # Valid nested list
        valid_list = [["item1", "item2"], ["item3", "item4"]]
        assert validator.validate(valid_list) is True

        # Invalid deeply nested list
        invalid_list = [[[[["too deep"]]]]]
        assert validator.validate(invalid_list) is False

    def test_json_depth_validator_mixed_nesting(self):
        """Test JSONDepthValidator with mixed dict/list nesting."""
        validator = JSONDepthValidator(max_depth=4)

        # Valid mixed nesting
        valid_mixed = {
            "data": [{"items": ["item1", "item2"]}, {"items": ["item3", "item4"]}]
        }
        assert validator.validate(valid_mixed) is True

        # Invalid mixed nesting
        invalid_mixed = {"data": [{"items": [{"nested": {"too": "deep"}}]}]}
        assert validator.validate(invalid_mixed) is False

    def test_json_depth_validator_depth_tracking(self):
        """Test JSONDepthValidator depth tracking accuracy."""
        validator = JSONDepthValidator(max_depth=3)

        # Test exact depth limit
        exact_limit_data = {"l1": {"l2": {"l3": "value"}}}  # Depth = 3
        assert validator.validate(exact_limit_data) is True

        # Test one level over limit
        over_limit_data = {"l1": {"l2": {"l3": {"l4": "value"}}}}  # Depth = 4
        assert validator.validate(over_limit_data) is False

    def test_json_depth_validator_error_handling(self):
        """Test JSONDepthValidator error handling."""
        validator = JSONDepthValidator()

        # Test with None
        assert validator.validate(None) is True

        # Test with simple types
        assert validator.validate("string") is True
        assert validator.validate(123) is True
        assert validator.validate(True) is True

    @patch("src.core.security.input_validators.logger")
    def test_json_depth_validator_logging(self, mock_logger):
        """Test JSONDepthValidator logging functionality."""
        validator = JSONDepthValidator(max_depth=2)
        validator.logger = mock_logger

        # Test deep data that should trigger warning
        deep_data = {"l1": {"l2": {"l3": "too deep"}}}
        validator.validate(deep_data)

        # Should have logged a warning about depth limit
        mock_logger.warning.assert_called()
        warning_call = mock_logger.warning.call_args[0][0]
        assert "JSON depth" in warning_call and "exceeds limit" in warning_call


class TestSecurityValidator:
    """Test suite for SecurityValidator class."""

    def test_security_validator_initialization_default(self):
        """Test SecurityValidator initialization with default settings."""
        validator = SecurityValidator()

        assert len(validator.validators) >= 4  # XSS, Unicode, PayloadSize, JSONDepth
        assert validator.use_unified_security in [
            True,
            False,
        ]  # Depends on availability

    def test_security_validator_initialization_custom(self):
        """Test SecurityValidator initialization with custom settings."""
        validator = SecurityValidator(
            max_payload_size=5 * 1024 * 1024,  # 5MB
            max_json_depth=50,
            enable_xss_protection=False,
            enable_unicode_validation=False,
            use_unified_security=False,
        )

        # Should have only PayloadSize and JSONDepth validators
        assert len(validator.validators) == 2
        assert validator.use_unified_security is False

    def test_security_validator_all_validations_pass(self):
        """Test SecurityValidator when all validations pass."""
        validator = SecurityValidator(
            max_payload_size=1024,
            max_json_depth=5,
            use_unified_security=False,  # Disable for consistent testing
        )

        safe_data = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "details": {"age": 30, "hobbies": ["reading", "swimming"]},
        }

        assert validator.validate(safe_data) is True

    def test_security_validator_xss_validation_fails(self):
        """Test SecurityValidator when XSS validation fails."""
        validator = SecurityValidator(use_unified_security=False)

        dangerous_data = {"name": "John Doe", "bio": "<script>alert('xss')</script>"}

        assert validator.validate(dangerous_data) is False

    def test_security_validator_unicode_validation_fails(self):
        """Test SecurityValidator when Unicode validation fails."""
        validator = SecurityValidator(use_unified_security=False)

        dangerous_data = {
            "name": "John\u0000Doe",  # Null byte
            "email": "<EMAIL>",
        }

        assert validator.validate(dangerous_data) is False

    def test_security_validator_payload_size_fails(self):
        """Test SecurityValidator when payload size validation fails."""
        validator = SecurityValidator(
            max_payload_size=100,  # Very small limit
            use_unified_security=False,
        )

        large_data = {
            "data": "A" * 200  # Larger than 100 bytes
        }

        assert validator.validate(large_data) is False

    def test_security_validator_json_depth_fails(self):
        """Test SecurityValidator when JSON depth validation fails."""
        validator = SecurityValidator(
            max_json_depth=2,  # Very shallow limit
            use_unified_security=False,
        )

        deep_data = {"level1": {"level2": {"level3": "too deep"}}}

        assert validator.validate(deep_data) is False

    def test_security_validator_multiple_failures(self):
        """Test SecurityValidator with multiple validation failures."""
        validator = SecurityValidator(
            max_payload_size=100, max_json_depth=2, use_unified_security=False
        )

        dangerous_data = {
            "bio": "<script>alert('xss')</script>",  # XSS
            "name": "John\u0000Doe",  # Unicode
            "large_field": "A" * 200,  # Size
            "deep": {"very": {"deep": "structure"}},  # Depth
        }

        assert validator.validate(dangerous_data) is False

    def test_security_validator_sanitize_functionality(self):
        """Test SecurityValidator sanitization functionality."""
        validator = SecurityValidator(use_unified_security=False)

        dangerous_data = {
            "bio": "<script>alert('xss')</script>",
            "name": "John\u0000Doe",
            "safe_field": "Normal text",
        }

        sanitized = validator.sanitize(dangerous_data)

        # Should sanitize dangerous content
        assert "&lt;script&gt;" in sanitized["bio"]
        assert "\u0000" not in sanitized["name"]
        assert sanitized["safe_field"] == "Normal text"

    @patch("src.core.security.input_validators.UNIFIED_SECURITY_AVAILABLE", True)
    @patch("src.core.security.input_validators.get_unified_security_validator")
    def test_security_validator_unified_security_integration(self, mock_get_validator):
        """Test SecurityValidator integration with unified security system."""
        # Mock unified security validator
        mock_unified_validator = Mock()
        mock_unified_validator.validate_and_sanitize.return_value = (
            True,
            {"sanitized": "data"},
        )
        mock_get_validator.return_value = mock_unified_validator

        validator = SecurityValidator(use_unified_security=True)

        # Should have initialized unified security validator
        assert validator.unified_security_validator is not None

        # Test validation with security context
        test_data = {"test": "data"}
        result = validator.validate(test_data)

        # Should use unified security validation
        assert isinstance(result, bool)

    @patch("src.core.security.input_validators.UNIFIED_SECURITY_AVAILABLE", False)
    def test_security_validator_fallback_without_unified_security(self):
        """Test SecurityValidator fallback when unified security is not available."""
        validator = SecurityValidator(use_unified_security=True)

        # Should fall back to legacy validation
        assert validator.use_unified_security is False
        assert validator.unified_security_validator is None

    def test_security_validator_error_handling(self):
        """Test SecurityValidator error handling."""
        validator = SecurityValidator(use_unified_security=False)

        # Test with None
        assert validator.validate(None) is True

        # Test with various data types
        assert validator.validate("string") is True
        assert validator.validate(123) is True
        assert validator.validate([]) is True

    @patch("src.core.security.input_validators.logger")
    def test_security_validator_logging(self, mock_logger):
        """Test SecurityValidator logging functionality."""
        validator = SecurityValidator(use_unified_security=False)
        validator.logger = mock_logger

        # Test with data that should trigger validation warnings
        dangerous_data = "<script>alert('xss')</script>"
        validator.validate(dangerous_data)

        # Should have logged warnings through individual validators
        mock_logger.warning.assert_called()
