# backend/core/services/project_service.py
"""Project Service Layer.

This module contains the business logic for Project entity operations including:
- Project creation, retrieval, updating, and deletion
- Business validation and rules enforcement
- Transaction management and orchestration
- Error handling and logging
"""

from src.core.utils.performance_utils import performance_monitor
from src.config.logging_config import logger
from src.core.errors.exceptions import (
    DataValidationError,
    ProjectNotFoundError,
)
from src.core.models.general.project import Project
from src.core.repositories.project_repository import ProjectRepository
from src.core.schemas.general.project_schemas import (
    ProjectCreateSchema,
    ProjectListResponseSchema,
    ProjectReadSchema,
    ProjectSummarySchema,
    ProjectUpdateSchema,
)

# Unified systems imports
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.monitoring.unified_performance_monitor import monitor_service_performance

# Import utilities for enhanced functionality
from src.core.utils import (
    # Pagination utilities for enhanced list operations
    PaginationPara<PERSON>,
    Query<PERSON>uilder,
    SortParams,
    create_pagination_response,
    paginate_query,
    sanitize_text,
    # String utilities for data processing
    slugify,
    # DateTime utilities for timezone-aware operations
    utcnow_aware,
)


class ProjectService:
    """Service class for Project entity business logic.

    This service handles all business operations related to projects including
    CRUD operations, validation, and orchestration with other services.
    """

    def __init__(self, project_repository: ProjectRepository):
        """Initialize the Project service.

        Args:
            project_repository: Repository for Project data access

        """
        self.project_repository = project_repository
        logger.debug(
            f"ProjectService initialized with repository: {type(project_repository).__name__}"
        )

    @handle_service_errors("create_project")
    @monitor_service_performance("create_project")
    def create_project(self, project_data: ProjectCreateSchema) -> ProjectReadSchema:
        """Create a new project with business validation.

        Args:
            project_data: Validated project creation data

        Returns:
            ProjectReadSchema: Created project data

        Raises:
            DuplicateEntryError: If project name or number already exists
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails

        """
        logger.info(
            f"Attempting to create new project: '{project_data.name}' ({project_data.project_number})"
        )

        # Business validation
        self._validate_project_creation(project_data)

        # Convert schema to dict for repository
        project_dict = project_data.model_dump()

        # Enhance project data with utilities
        project_dict.update(
            {
                # Sanitize description for security (remove HTML tags)
                "description": sanitize_text(project_data.description or ""),
                # Note: Timestamps are handled by the database (func.now())
                # but we could override them with timezone-aware versions if needed
            }
        )

        # Generate slug for future use (could be stored in metadata or used for URLs)
        project_slug = slugify(project_data.name)
        logger.debug(
            f"Generated slug for project '{project_data.name}': '{project_slug}'"
        )

        # Create project via repository
        new_project = self.project_repository.create(project_dict)

        # Commit the transaction (repository doesn't auto-commit)
        self.project_repository.db_session.commit()
        self.project_repository.db_session.refresh(new_project)

        logger.info(
            f"Project '{new_project.name}' (ID: {new_project.id}) created successfully"
        )

        # Convert to read schema
        return ProjectReadSchema.model_validate(new_project)

    @handle_service_errors("get_project_details")
    @monitor_service_performance("get_project_details")
    @performance_monitor("get_project_details")
    def get_project_details(self, project_id: str) -> ProjectReadSchema:
        """Retrieve detailed project information.

        Args:
            project_id: Project ID (can be numeric ID or project number)

        Returns:
            ProjectReadSchema: Project details

        Raises:
            ProjectNotFoundError: If project doesn't exist
            DatabaseError: If database operation fails

        """
        logger.debug(f"Retrieving project details for ID: {project_id}")

        # Try to get by numeric ID first, then by project number
        project = None

        if project_id.isdigit():
            project = self.project_repository.get_by_id(int(project_id))

        if project is None:
            # Try by project number with proper error context
            try:
                project = self.project_repository.get_by_code(project_id)
                logger.debug(f"Project found by code: {project_id}")
            except ProjectNotFoundError:
                logger.debug(f"Project not found by code: {project_id}")
                # Continue to final validation - unified error handler will manage the final exception

        if project is None:
            logger.warning(f"Project not found: {project_id}")
            raise ProjectNotFoundError(project_id=project_id)

        # Check if project is soft deleted
        if project.is_deleted:
            logger.warning(f"Attempted to access deleted project: {project_id}")
            raise ProjectNotFoundError(project_id=project_id)

        logger.debug(f"Project found: '{project.name}' (ID: {project.id})")
        return ProjectReadSchema.model_validate(project)

    @handle_service_errors("update_project")
    @monitor_service_performance("update_project")
    def update_project(
        self, project_id: str, project_data: ProjectUpdateSchema
    ) -> ProjectReadSchema:
        """Update an existing project.

        Args:
            project_id: Project ID (can be numeric ID or project number)
            project_data: Validated project update data

        Returns:
            ProjectReadSchema: Updated project data

        Raises:
            ProjectNotFoundError: If project doesn't exist
            DuplicateEntryError: If updated name/number conflicts
            DatabaseError: If database operation fails

        """
        logger.info(f"Attempting to update project: {project_id}")

        # Get existing project
        existing_project = self._get_project_by_id_or_code(project_id)

        # Business validation for updates
        self._validate_project_update(existing_project, project_data)

        # Update only provided fields
        update_dict = project_data.model_dump(exclude_unset=True)

        if not update_dict:
            logger.debug(f"No fields to update for project {project_id}")
            return ProjectReadSchema.model_validate(existing_project)

        # Enhance update data with utilities
        if "description" in update_dict:
            # Sanitize description for security
            update_dict["description"] = sanitize_text(update_dict["description"] or "")

        # Note: updated_at timestamp is handled automatically by the database
        # but we could override it with timezone-aware version if needed
        update_dict["updated_at"] = utcnow_aware()

        # Update the project
        for field, value in update_dict.items():
            setattr(existing_project, field, value)

        # Commit the transaction
        self.project_repository.db_session.commit()
        self.project_repository.db_session.refresh(existing_project)

        logger.info(
            f"Project '{existing_project.name}' (ID: {existing_project.id}) updated successfully"
        )
        return ProjectReadSchema.model_validate(existing_project)

    @handle_service_errors("delete_project")
    @monitor_service_performance("delete_project")
    def delete_project(
        self, project_id: str, deleted_by_user_id: int | None = None
    ) -> None:
        """Soft delete a project.

        Args:
            project_id: Project ID (can be numeric ID or project number)
            deleted_by_user_id: ID of user performing the deletion

        Raises:
            ProjectNotFoundError: If project doesn't exist
            DatabaseError: If database operation fails

        """
        logger.info(f"Attempting to delete project: {project_id}")

        # Get existing project
        existing_project = self._get_project_by_id_or_code(project_id)

        if existing_project.is_deleted:
            logger.warning(f"Project {project_id} is already deleted")
            raise ProjectNotFoundError(project_id=project_id)

        # Perform soft delete using timezone-aware datetime
        existing_project.is_deleted = True
        existing_project.deleted_at = utcnow_aware()
        existing_project.deleted_by_user_id = deleted_by_user_id

        # Commit the transaction
        self.project_repository.db_session.commit()

        logger.info(
            f"Project '{existing_project.name}' (ID: {existing_project.id}) deleted successfully"
        )

    @handle_service_errors("get_projects_list")
    @monitor_service_performance("get_projects_list")
    def get_projects_list(
        self, page: int = 1, per_page: int = 10, include_deleted: bool = False
    ) -> ProjectListResponseSchema:
        """Get paginated list of projects.

        Args:
            page: Page number (1-based)
            per_page: Number of projects per page
            include_deleted: Whether to include soft-deleted projects

        Returns:
            ProjectListResponseSchema: Paginated project list

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(
            f"Retrieving projects list: page={page}, per_page={per_page}, include_deleted={include_deleted}"
        )

        # Calculate offset
        skip = (page - 1) * per_page

        # Get projects from repository
        projects = self.project_repository.get_all(skip=skip, limit=per_page)

        # Filter out deleted projects if not requested
        if not include_deleted:
            projects = [p for p in projects if not p.is_deleted]

        # Get total count (this is simplified - in production you'd want a separate count query)
        all_projects = self.project_repository.get_all(
            skip=0, limit=1000
        )  # Simplified for now
        if not include_deleted:
            all_projects = [p for p in all_projects if not p.is_deleted]
        total = len(all_projects)

        # Convert to summary schemas
        project_summaries = [ProjectSummarySchema.model_validate(p) for p in projects]

        # Calculate total pages
        import math

        total_pages = math.ceil(total / per_page) if total > 0 else 1

        logger.debug(f"Retrieved {len(project_summaries)} projects (total: {total})")

        return ProjectListResponseSchema(
            projects=project_summaries,
            total=total,
            page=page,
            per_page=per_page,
            total_pages=total_pages,
        )

    @handle_service_errors("get_projects_paginated")
    @monitor_service_performance("get_projects_paginated")
    @performance_monitor("get_projects_paginated")
    def get_projects_paginated(
        self,
        pagination_params: PaginationParams,
        sort_params: SortParams | None = None,
        filters: dict | None = None,
    ) -> dict:
        """Get paginated list of projects with enhanced search and sorting.

        This method uses the new pagination utilities for better performance
        and enhanced functionality compared to get_projects_list().

        Args:
            pagination_params: Pagination parameters (page, per_page)
            sort_params: Optional sorting parameters
            filters: Optional filters (search, include_deleted, etc.)

        Returns:
            dict: Paginated response with projects and metadata

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(
            f"Retrieving paginated projects: page={pagination_params.page}, "
            f"per_page={pagination_params.per_page}, sort={sort_params}, filters={filters}"
        )

        # Build base query using QueryBuilder
        builder = QueryBuilder(self.project_repository.db_session, Project)

        # Apply filters
        if filters:
            # Handle soft delete filter
            include_deleted = filters.get("include_deleted", False)
            if not include_deleted:
                builder.filter_by_field("is_deleted", False)

            # Handle search filter
            search_term = filters.get("search")
            if search_term:
                searchable_fields = ["name", "description", "project_number"]
                builder.filter_by_text_search(search_term, searchable_fields)
        else:
            # Default: exclude deleted projects
            builder.filter_by_field("is_deleted", False)

        # Build the query
        query = builder.build()

        # Apply pagination and sorting using utilities
        allowed_sort_fields = ["name", "created_at", "updated_at", "project_number"]
        result = paginate_query(
            self.project_repository.db_session,
            query,
            Project,
            pagination_params,
            sort_params,
            allowed_sort_fields,
        )

        # Convert to summary schemas
        project_summaries = [
            ProjectSummarySchema.model_validate(p) for p in result.items
        ]

        # Create standardized response
        response = create_pagination_response(project_summaries, result)

        logger.debug(
            f"Retrieved {len(project_summaries)} projects (total: {result.total})"
        )

        return response

    @handle_service_errors("_get_project_by_id_or_code")
    def _get_project_by_id_or_code(self, project_id: str) -> Project:
        """Helper method to get project by ID or project number.

        Args:
            project_id: Project ID (can be numeric ID or project number)

        Returns:
            Project: The found project

        Raises:
            ProjectNotFoundError: If project doesn't exist

        """
        project = None

        if project_id.isdigit():
            project = self.project_repository.get_by_id(int(project_id))

        if project is None:
            # Try by project number with proper error context
            try:
                project = self.project_repository.get_by_code(project_id)
                logger.debug(f"Project found by code in helper method: {project_id}")
            except ProjectNotFoundError:
                logger.debug(
                    f"Project not found by code in helper method: {project_id}"
                )
                # Continue to final validation - unified error handler will manage the final exception

        if project is None or project.is_deleted:
            raise ProjectNotFoundError(project_id=project_id)

        return project

    @handle_service_errors("_validate_project_creation")
    def _validate_project_creation(self, project_data: ProjectCreateSchema) -> None:
        """Perform business validation for project creation.

        Args:
            project_data: Project creation data to validate

        Raises:
            DataValidationError: If business validation fails

        """
        # Temperature validation
        if project_data.max_ambient_temp_c <= project_data.min_ambient_temp_c:
            raise DataValidationError(
                details={
                    "temperature_range": "Maximum ambient temperature must be greater than minimum ambient temperature"
                }
            )

        # Maintenance temperature validation
        if project_data.desired_maintenance_temp_c <= project_data.max_ambient_temp_c:
            raise DataValidationError(
                details={
                    "maintenance_temperature": f"Desired maintenance temperature ({project_data.desired_maintenance_temp_c}°C) must be greater than maximum ambient temperature ({project_data.max_ambient_temp_c}°C)"
                }
            )

    @handle_service_errors("_validate_project_update")
    def _validate_project_update(
        self, existing_project: Project, project_data: ProjectUpdateSchema
    ) -> None:
        """Perform business validation for project updates.

        Args:
            existing_project: Current project data
            project_data: Update data to validate

        Raises:
            DataValidationError: If business validation fails

        """
        # Get effective values (updated or existing)
        min_temp = (
            project_data.min_ambient_temp_c
            if project_data.min_ambient_temp_c is not None
            else existing_project.min_ambient_temp_c
        )
        max_temp = (
            project_data.max_ambient_temp_c
            if project_data.max_ambient_temp_c is not None
            else existing_project.max_ambient_temp_c
        )
        maintenance_temp = (
            project_data.desired_maintenance_temp_c
            if project_data.desired_maintenance_temp_c is not None
            else existing_project.desired_maintenance_temp_c
        )

        # Temperature range validation
        if max_temp <= min_temp:
            raise DataValidationError(
                details={
                    "temperature_range": "Maximum ambient temperature must be greater than minimum ambient temperature"
                }
            )

        # Maintenance temperature validation
        if maintenance_temp <= max_temp:
            raise DataValidationError(
                details={
                    "maintenance_temperature": f"Desired maintenance temperature ({maintenance_temp}°C) must be greater than maximum ambient temperature ({max_temp}°C)"
                }
            )
