# src/middleware/logging_middleware.py
"""Logging Middleware for Ultimate Electrical Designer Backend.

This module provides comprehensive request/response logging including:
- Request metadata logging (method, path, headers, client info)
- Response metadata logging (status, size, timing)
- Error logging with context
- Performance metrics collection
- Structured logging for monitoring
"""

import json
import time
from typing import Any

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from src.config.logging_config import logger
from src.middleware.context_middleware import get_request_id, get_user_context


class LoggingMiddleware(BaseHTTPMiddleware):
    """Logging middleware for comprehensive request/response logging.

    Features:
    - Structured request/response logging
    - Performance metrics collection
    - Error context logging
    - Client information tracking
    - Configurable log levels and filtering
    """

    def __init__(
        self,
        app: ASGIApp,
        enable_request_logging: bool = True,
        enable_response_logging: bool = True,
        enable_error_logging: bool = True,
        enable_performance_logging: bool = True,
        log_request_body: bool = False,
        log_response_body: bool = False,
        max_body_size: int = 1024,  # Max body size to log in bytes
        exclude_paths: set[str] | None = None,
        exclude_health_checks: bool = True,
    ):
        super().__init__(app)
        self.enable_request_logging = enable_request_logging
        self.enable_response_logging = enable_response_logging
        self.enable_error_logging = enable_error_logging
        self.enable_performance_logging = enable_performance_logging
        self.log_request_body = log_request_body
        self.log_response_body = log_response_body
        self.max_body_size = max_body_size

        # Default excluded paths
        default_excluded = {
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/favicon.ico",
        }
        if exclude_paths:
            default_excluded.update(exclude_paths)
        self.exclude_paths = default_excluded
        self.exclude_health_checks = exclude_health_checks

    async def dispatch(self, request: Request, call_next) -> Response:
        """Main middleware dispatch method that handles request/response logging."""
        start_time = time.time()

        # Check if path should be excluded from logging
        if self._should_exclude_path(request.url.path):
            return await call_next(request)

        # Log request
        request_data = None
        if self.enable_request_logging:
            request_data = await self._log_request(request)

        try:
            # Process the request
            response = await call_next(request)

            # Log successful response
            if self.enable_response_logging:
                await self._log_response(request, response, start_time, request_data)

            # Log performance metrics
            if self.enable_performance_logging:
                self._log_performance_metrics(request, response, start_time)

            return response

        except Exception as e:
            # Log error
            if self.enable_error_logging:
                await self._log_error(request, e, start_time, request_data)
            raise

    def _should_exclude_path(self, path: str) -> bool:
        """Check if the path should be excluded from logging."""
        if self.exclude_health_checks and any(
            health_path in path.lower()
            for health_path in ["/health", "/ping", "/status"]
        ):
            return True

        return path in self.exclude_paths

    async def _log_request(self, request: Request) -> dict[str, Any]:
        """Log incoming request details."""
        request_data = {
            "request_id": get_request_id(),
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent", ""),
            "content_type": request.headers.get("content-type", ""),
            "content_length": request.headers.get("content-length", "0"),
            "user_context": get_user_context(),
        }

        # Add request body if enabled and size is reasonable
        if self.log_request_body and self._should_log_body(request):
            try:
                body = await request.body()
                if len(body) <= self.max_body_size:
                    # Try to parse as JSON for better logging
                    try:
                        request_data["body"] = json.loads(body.decode())
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        request_data["body"] = body.decode(errors="replace")[
                            : self.max_body_size
                        ]
                else:
                    request_data["body_size"] = len(body)
                    request_data["body"] = f"<body too large: {len(body)} bytes>"
            except Exception as e:
                request_data["body_error"] = str(e)

        logger.info(
            f"Request started - {request.method} {request.url.path}",
            extra={"request_data": request_data},
        )

        return request_data

    async def _log_response(
        self,
        request: Request,
        response: Response,
        start_time: float,
        request_data: dict[str, Any] | None,
    ) -> None:
        """Log response details."""
        duration = time.time() - start_time

        response_data = {
            "request_id": get_request_id(),
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2),
            "response_size": len(response.body) if hasattr(response, "body") else 0,
            "content_type": response.headers.get("content-type", ""),
        }

        # Add response body if enabled and size is reasonable
        if self.log_response_body and hasattr(response, "body"):
            body = response.body
            if isinstance(body, bytes) and len(body) <= self.max_body_size:
                try:
                    response_data["body"] = json.loads(body.decode())
                except (json.JSONDecodeError, UnicodeDecodeError):
                    response_data["body"] = body.decode(errors="replace")[
                        : self.max_body_size
                    ]

        # Determine log level based on status code
        if response.status_code >= 500:
            log_level = "error"
        elif response.status_code >= 400:
            log_level = "warning"
        else:
            log_level = "info"

        getattr(logger, log_level)(
            f"Request completed - {request.method} {request.url.path} "
            f"[{response.status_code}] in {duration:.3f}s",
            extra={"response_data": response_data, "request_data": request_data},
        )

    async def _log_error(
        self,
        request: Request,
        error: Exception,
        start_time: float,
        request_data: dict[str, Any] | None,
    ) -> None:
        """Log error details."""
        duration = time.time() - start_time

        error_data = {
            "request_id": get_request_id(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "duration_ms": round(duration * 1000, 2),
            "user_context": get_user_context(),
        }

        logger.error(
            "Request failed - {} {} Error: {}: {}",
            request.method,
            request.url.path,
            type(error).__name__,
            str(error),
            extra={"error_data": error_data, "request_data": request_data},
            exc_info=True,
        )

    def _log_performance_metrics(
        self, request: Request, response: Response, start_time: float
    ) -> None:
        """Log performance metrics."""
        duration = time.time() - start_time

        # Log slow requests
        slow_threshold = 2.0  # seconds
        if duration > slow_threshold:
            logger.warning(
                f"Slow request detected - {request.method} {request.url.path} "
                f"took {duration:.3f}s (threshold: {slow_threshold}s)",
                extra={
                    "performance_data": {
                        "request_id": get_request_id(),
                        "duration": duration,
                        "threshold": slow_threshold,
                        "status_code": response.status_code,
                        "user_context": get_user_context(),
                    }
                },
            )

        # Log performance metrics for monitoring
        logger.debug(
            f"Performance metrics - {request.method} {request.url.path}",
            extra={
                "metrics": {
                    "request_id": get_request_id(),
                    "method": request.method,
                    "path": request.url.path,
                    "status_code": response.status_code,
                    "duration_ms": round(duration * 1000, 2),
                    "response_size": len(response.body)
                    if hasattr(response, "body")
                    else 0,
                }
            },
        )

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers first (reverse proxy)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # X-Forwarded-For can contain multiple IPs, take the first one
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip

        # Fallback to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host

        return "unknown"

    def _should_log_body(self, request: Request) -> bool:
        """Determine if request body should be logged."""
        content_type = request.headers.get("content-type", "")

        # Only log text-based content types
        text_types = [
            "application/json",
            "application/xml",
            "text/",
            "application/x-www-form-urlencoded",
        ]

        return any(text_type in content_type for text_type in text_types)
