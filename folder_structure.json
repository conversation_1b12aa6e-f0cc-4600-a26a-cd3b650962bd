{"Path": "D:\\Projects\\ultimate-electrical-designer", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\api", "Children": [], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\plugins", "Children": [], "Name": "plugins", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Controllers", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Controllers\\CadController.cs", "Extension": ".cs", "SizeKB": 0.0, "Name": "CadController.cs", "Type": "File"}], "Name": "Controllers", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Services", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\Services\\AutoCADService.cs", "Extension": ".cs", "SizeKB": 0.0, "Name": "AutoCADService.cs", "Type": "File"}], "Name": "Services", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\src\\ultimate_electrical_designer.CadIntegrator.csproj", "Extension": ".c<PERSON><PERSON>j", "SizeKB": 0.0, "Name": "ultimate_electrical_designer.CadIntegrator.csproj", "Type": "File"}], "Name": "src", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\Dockerfile", "Extension": "", "SizeKB": 0.0, "Name": "Dockerfile", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\cad-integrator-service\\README.md", "Extension": ".md", "SizeKB": 1.4, "Name": "README.md", "Type": "File"}], "Name": "cad-integrator-service", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)\\login", "Children": [], "Name": "login", "Type": "Folder"}], "Name": "(auth)", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\api\\auth", "Children": [], "Name": "auth", "Type": "Folder"}], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\dashboard", "Children": [], "Name": "dashboard", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\README.md", "Extension": ".md", "SizeKB": 4.98, "Name": "README.md", "Type": "File"}], "Name": "app", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\assets", "Children": [], "Name": "assets", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.25, "Name": "index.ts.txt", "Type": "File"}], "Name": "common", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\domain", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\domain\\project", "Children": [], "Name": "project", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\domain\\user", "Children": [], "Name": "user", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\domain\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.27, "Name": "index.ts.txt", "Type": "File"}], "Name": "domain", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.23, "Name": "index.ts.txt", "Type": "File"}], "Name": "ui", "Type": "Folder"}], "Name": "components", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.27, "Name": "index.ts.txt", "Type": "File"}], "Name": "hooks", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.28, "Name": "index.ts.txt", "Type": "File"}], "Name": "lib", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\api\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.22, "Name": "index.ts.txt", "Type": "File"}], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\components\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.2, "Name": "index.ts.txt", "Type": "File"}], "Name": "components", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\hooks\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.15, "Name": "index.ts.txt", "Type": "File"}], "Name": "hooks", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\cable_sizing\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.42, "Name": "index.ts.txt", "Type": "File"}], "Name": "cable_sizing", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\api\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.22, "Name": "index.ts.txt", "Type": "File"}], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\components\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.2, "Name": "index.ts.txt", "Type": "File"}], "Name": "components", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\hooks\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.15, "Name": "index.ts.txt", "Type": "File"}], "Name": "hooks", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\circuits\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.42, "Name": "index.ts.txt", "Type": "File"}], "Name": "circuits", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\api\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.22, "Name": "index.ts.txt", "Type": "File"}], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\components\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.2, "Name": "index.ts.txt", "Type": "File"}], "Name": "components", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\hooks\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.15, "Name": "index.ts.txt", "Type": "File"}], "Name": "hooks", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\components\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.42, "Name": "index.ts.txt", "Type": "File"}], "Name": "components", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\api\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.22, "Name": "index.ts.txt", "Type": "File"}], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\components\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.2, "Name": "index.ts.txt", "Type": "File"}], "Name": "components", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\hooks\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.15, "Name": "index.ts.txt", "Type": "File"}], "Name": "hooks", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\heat_tracing\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.42, "Name": "index.ts.txt", "Type": "File"}], "Name": "heat_tracing", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\api\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.22, "Name": "index.ts.txt", "Type": "File"}], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\components\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.2, "Name": "index.ts.txt", "Type": "File"}], "Name": "components", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\hooks\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.15, "Name": "index.ts.txt", "Type": "File"}], "Name": "hooks", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\load_calculations\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.42, "Name": "index.ts.txt", "Type": "File"}], "Name": "load_calculations", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\api\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.22, "Name": "index.ts.txt", "Type": "File"}], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\components\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.2, "Name": "index.ts.txt", "Type": "File"}], "Name": "components", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\hooks\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.15, "Name": "index.ts.txt", "Type": "File"}], "Name": "hooks", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\projects\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.42, "Name": "index.ts.txt", "Type": "File"}], "Name": "projects", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\api\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.22, "Name": "index.ts.txt", "Type": "File"}], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\components", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\components\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.2, "Name": "index.ts.txt", "Type": "File"}], "Name": "components", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\hooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\hooks\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.15, "Name": "index.ts.txt", "Type": "File"}], "Name": "hooks", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\modules\\users\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.42, "Name": "index.ts.txt", "Type": "File"}], "Name": "users", "Type": "Folder"}], "Name": "modules", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\services", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\services\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.31, "Name": "index.ts.txt", "Type": "File"}], "Name": "services", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\styles", "Children": [], "Name": "styles", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\types", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\types\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.26, "Name": "index.ts.txt", "Type": "File"}], "Name": "types", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\utils", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\client\\src\\utils\\index.ts.txt", "Extension": ".txt", "SizeKB": 0.24, "Name": "index.ts.txt", "Type": "File"}], "Name": "utils", "Type": "Folder"}], "Name": "src", "Type": "Folder"}], "Name": "client", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\api", "Children": [], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\plugins", "Children": [], "Name": "plugins", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Controllers", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Controllers\\ComputationController.cs", "Extension": ".cs", "SizeKB": 0.0, "Name": "ComputationController.cs", "Type": "File"}], "Name": "Controllers", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Services", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\Services\\PowerFlowSolver.cs", "Extension": ".cs", "SizeKB": 0.0, "Name": "PowerFlowSolver.cs", "Type": "File"}], "Name": "Services", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\src\\ultimate_electrical_designer.ComputationEngine.csproj", "Extension": ".c<PERSON><PERSON>j", "SizeKB": 0.0, "Name": "ultimate_electrical_designer.ComputationEngine.csproj", "Type": "File"}], "Name": "src", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\Dockerfile", "Extension": "", "SizeKB": 0.0, "Name": "Dockerfile", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\computation-engine-service\\README.md", "Extension": ".md", "SizeKB": 1.17, "Name": "README.md", "Type": "File"}], "Name": "computation-engine-service", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\data", "Children": [], "Name": "data", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\agent-implementation-guides.md", "Extension": ".md", "SizeKB": 20.92, "Name": "agent-implementation-guides.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\agent-training.md", "Extension": ".md", "SizeKB": 21.14, "Name": "agent-training.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\coordination-protocols.md", "Extension": ".md", "SizeKB": 12.04, "Name": "coordination-protocols.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\framework-summary.md", "Extension": ".md", "SizeKB": 9.41, "Name": "framework-summary.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\index.md", "Extension": ".md", "SizeKB": 10.21, "Name": "index.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\performance-monitoring.md", "Extension": ".md", "SizeKB": 17.86, "Name": "performance-monitoring.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\quality-assurance.md", "Extension": ".md", "SizeKB": 14.22, "Name": "quality-assurance.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\ai-agent-team\\README.md", "Extension": ".md", "SizeKB": 22.62, "Name": "README.md", "Type": "File"}], "Name": "ai-agent-team", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\calculations", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\calculations\\001-electrical-calculations.md", "Extension": ".md", "SizeKB": 47.47, "Name": "001-electrical-calculations.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\calculations\\002-heat-loss-calculations.md", "Extension": ".md", "SizeKB": 27.33, "Name": "002-heat-loss-calculations.md", "Type": "File"}], "Name": "calculations", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\backend", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\backend\\000-backend-specification.md", "Extension": ".md", "SizeKB": 19.82, "Name": "000-backend-specification.md", "Type": "File"}], "Name": "backend", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\001-frontend-development-handbook.md", "Extension": ".md", "SizeKB": 13.74, "Name": "001-frontend-development-handbook.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\002-ui-implementation-guide.md", "Extension": ".md", "SizeKB": 22.06, "Name": "002-ui-implementation-guide.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\003-linting-guide.md", "Extension": ".md", "SizeKB": 14.54, "Name": "003-linting-guide.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\004-documentation-guide.md", "Extension": ".md", "SizeKB": 13.49, "Name": "004-documentation-guide.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\005-state-management.md", "Extension": ".md", "SizeKB": 4.87, "Name": "005-state-management.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\006-data-fetching-api.md", "Extension": ".md", "SizeKB": 4.86, "Name": "006-data-fetching-api.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\007-testing.md", "Extension": ".md", "SizeKB": 2.99, "Name": "007-testing.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\008-routing-navigation.md", "Extension": ".md", "SizeKB": 2.53, "Name": "008-routing-navigation.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\009-performance-optimization.md", "Extension": ".md", "SizeKB": 3.17, "Name": "009-performance-optimization.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-developer-handbook\\010-deployment-environment.md", "Extension": ".md", "SizeKB": 2.88, "Name": "010-deployment-environment.md", "Type": "File"}], "Name": "frontend-developer-handbook", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\001-typescript-handbook.md", "Extension": ".md", "SizeKB": 6.78, "Name": "001-typescript-handbook.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\002-core-typescript-concepts.md", "Extension": ".md", "SizeKB": 8.98, "Name": "002-core-typescript-concepts.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\003-typing-react-components-props.md", "Extension": ".md", "SizeKB": 6.0, "Name": "003-typing-react-components-props.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\004-typing-react-hooks.md", "Extension": ".md", "SizeKB": 10.23, "Name": "004-typing-react-hooks.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\005-typing-state-management.md", "Extension": ".md", "SizeKB": 5.56, "Name": "005-typing-state-management.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\006-typing-data-fetching-api-interaction.md", "Extension": ".md", "SizeKB": 10.1, "Name": "006-typing-data-fetching-api-interaction.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\007-typing-routing.md", "Extension": ".md", "SizeKB": 7.06, "Name": "007-typing-routing.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\008-typing-forms-validation.md", "Extension": ".md", "SizeKB": 10.65, "Name": "008-typing-forms-validation.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\009-typing-utility-functions-advanced-patterns.md", "Extension": ".md", "SizeKB": 8.87, "Name": "009-typing-utility-functions-advanced-patterns.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\frontend-typing-handbook\\010-typing-testing.md", "Extension": ".md", "SizeKB": 7.0, "Name": "010-typing-testing.md", "Type": "File"}], "Name": "frontend-typing-handbook", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend\\000-frontend-specification.md", "Extension": ".md", "SizeKB": 38.26, "Name": "000-frontend-specification.md", "Type": "File"}], "Name": "frontend", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend-transition", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend-transition\\001-backend-analysis-guide.md", "Extension": ".md", "SizeKB": 8.81, "Name": "001-backend-analysis-guide.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend-transition\\002-preparing-backend-workflows.md", "Extension": ".md", "SizeKB": 9.29, "Name": "002-preparing-backend-workflows.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\frontend-transition\\003-frontend-development-roadmap.md", "Extension": ".md", "SizeKB": 9.61, "Name": "003-frontend-development-roadmap.md", "Type": "File"}], "Name": "frontend-transition", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\templates", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\templates\\getting-started-template.md", "Extension": ".md", "SizeKB": 2.34, "Name": "getting-started-template.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\templates\\reference-template.md", "Extension": ".md", "SizeKB": 5.75, "Name": "reference-template.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\templates\\section-template.md", "Extension": ".md", "SizeKB": 5.12, "Name": "section-template.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\templates\\technical-guide-template.md", "Extension": ".md", "SizeKB": 5.26, "Name": "technical-guide-template.md", "Type": "File"}], "Name": "templates", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\000-cover-index.md", "Extension": ".md", "SizeKB": 11.03, "Name": "000-cover-index.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\001-cover.md", "Extension": ".md", "SizeKB": 6.0, "Name": "001-cover.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\002-navigation.md", "Extension": ".md", "SizeKB": 10.83, "Name": "002-navigation.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\010-introduction-index.md", "Extension": ".md", "SizeKB": 2.67, "Name": "010-introduction-index.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\011-introduction.md", "Extension": ".md", "SizeKB": 16.94, "Name": "011-introduction.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\020-getting-started.md", "Extension": ".md", "SizeKB": 11.39, "Name": "020-getting-started.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\030-development-standards.md", "Extension": ".md", "SizeKB": 15.03, "Name": "030-development-standards.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\040-unified-patterns.md", "Extension": ".md", "SizeKB": 16.79, "Name": "040-unified-patterns.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\050-backend-development.md", "Extension": ".md", "SizeKB": 72.7, "Name": "050-backend-development.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\060-frontend-transition.md", "Extension": ".md", "SizeKB": 21.94, "Name": "060-frontend-transition.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\070-testing-framework.md", "Extension": ".md", "SizeKB": 20.37, "Name": "070-testing-framework.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\080-script-ecosystem.md", "Extension": ".md", "SizeKB": 13.89, "Name": "080-script-ecosystem.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\090-component-models.md", "Extension": ".md", "SizeKB": 25.8, "Name": "090-component-models.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\100-database-management.md", "Extension": ".md", "SizeKB": 20.91, "Name": "100-database-management.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\developer-handbooks\\110-docker-dev-deployment.md", "Extension": ".md", "SizeKB": 22.29, "Name": "110-docker-dev-deployment.md", "Type": "File"}], "Name": "developer-handbooks", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\001-development-roadmap.md", "Extension": ".md", "SizeKB": 31.35, "Name": "001-development-roadmap.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\002-robust-design-principles.md", "Extension": ".md", "SizeKB": 7.81, "Name": "002-robust-design-principles.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\003-implementation-methodology.md", "Extension": ".md", "SizeKB": 14.0, "Name": "003-implementation-methodology.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\docs\\004-methodology-template.md", "Extension": ".md", "SizeKB": 8.47, "Name": "004-methodology-template.md", "Type": "File"}], "Name": "docs", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\data", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\data\\app_dev.db", "Extension": ".db", "SizeKB": 12.0, "Name": "app_dev.db", "Type": "File"}], "Name": "data", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic\\env.py", "Extension": ".py", "SizeKB": 4.7, "Name": "env.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic\\script.py.mako", "Extension": ".mako", "SizeKB": 0.64, "Name": "script.py.mako", "Type": "File"}], "Name": "alembic", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\v1", "Children": [], "Name": "v1", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\api\\main_router.py", "Extension": ".py", "SizeKB": 1.3, "Name": "main_router.py", "Type": "File"}], "Name": "api", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\config", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\config\\logging_config.py", "Extension": ".py", "SizeKB": 6.2, "Name": "logging_config.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\config\\settings.py", "Extension": ".py", "SizeKB": 5.85, "Name": "settings.py", "Type": "File"}], "Name": "config", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\calculations", "Children": [], "Name": "calculations", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\dependencies.py", "Extension": ".py", "SizeKB": 1.04, "Name": "dependencies.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\engine.py", "Extension": ".py", "SizeKB": 10.88, "Name": "engine.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\initialization.py", "Extension": ".py", "SizeKB": 6.04, "Name": "initialization.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\database\\session.py", "Extension": ".py", "SizeKB": 14.12, "Name": "session.py", "Type": "File"}], "Name": "database", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\calculation_enums.py", "Extension": ".py", "SizeKB": 2.53, "Name": "calculation_enums.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\common_enums.py", "Extension": ".py", "SizeKB": 2.3, "Name": "common_enums.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\data_io_enums.py", "Extension": ".py", "SizeKB": 6.12, "Name": "data_io_enums.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\electrical_enums.py", "Extension": ".py", "SizeKB": 24.55, "Name": "electrical_enums.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\heat_tracing_enums.py", "Extension": ".py", "SizeKB": 2.64, "Name": "heat_tracing_enums.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\mechanical_enums.py", "Extension": ".py", "SizeKB": 4.11, "Name": "mechanical_enums.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\project_management_enums.py", "Extension": ".py", "SizeKB": 4.86, "Name": "project_management_enums.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\standards_enums.py", "Extension": ".py", "SizeKB": 2.89, "Name": "standards_enums.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\enums\\system_enums.py", "Extension": ".py", "SizeKB": 4.73, "Name": "system_enums.py", "Type": "File"}], "Name": "enums", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\errors", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\errors\\exceptions.py", "Extension": ".py", "SizeKB": 8.44, "Name": "exceptions.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\errors\\unified_error_handler.py", "Extension": ".py", "SizeKB": 34.09, "Name": "unified_error_handler.py", "Type": "File"}], "Name": "errors", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations\\cad_service", "Children": [], "Name": "cad_service", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations\\computation service", "Children": [], "Name": "computation service", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations\\message_brokers", "Children": [], "Name": "message_brokers", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\integrations\\README.md", "Extension": ".md", "SizeKB": 1.88, "Name": "README.md", "Type": "File"}], "Name": "integrations", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\general\\user.py", "Extension": ".py", "SizeKB": 3.31, "Name": "user.py", "Type": "File"}], "Name": "general", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\models\\base.py", "Extension": ".py", "SizeKB": 7.07, "Name": "base.py", "Type": "File"}], "Name": "models", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\monitoring", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\monitoring\\unified_performance_monitor.py", "Extension": ".py", "SizeKB": 30.32, "Name": "unified_performance_monitor.py", "Type": "File"}], "Name": "monitoring", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\user_preference_repository.py", "Extension": ".py", "SizeKB": 3.56, "Name": "user_preference_repository.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\general\\user_repository.py", "Extension": ".py", "SizeKB": 5.93, "Name": "user_repository.py", "Type": "File"}], "Name": "general", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\repositories\\base_repository.py", "Extension": ".py", "SizeKB": 10.14, "Name": "base_repository.py", "Type": "File"}], "Name": "repositories", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\schemas\\error.py", "Extension": ".py", "SizeKB": 3.2, "Name": "error.py", "Type": "File"}], "Name": "schemas", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security\\enhanced_dependencies.py", "Extension": ".py", "SizeKB": 9.36, "Name": "enhanced_dependencies.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security\\input_validators.py", "Extension": ".py", "SizeKB": 43.31, "Name": "input_validators.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\security\\unified_security_validator.py", "Extension": ".py", "SizeKB": 37.47, "Name": "unified_security_validator.py", "Type": "File"}], "Name": "security", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\services\\design_automation", "Children": [], "Name": "design_automation", "Type": "Folder"}], "Name": "services", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\standards", "Children": [], "Name": "standards", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\datetime_utils.py", "Extension": ".py", "SizeKB": 8.21, "Name": "datetime_utils.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\json_validation.py", "Extension": ".py", "SizeKB": 12.07, "Name": "json_validation.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\memory_manager.py", "Extension": ".py", "SizeKB": 14.74, "Name": "memory_manager.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\pagination_utils.py", "Extension": ".py", "SizeKB": 9.3, "Name": "pagination_utils.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\performance_optimizer.py", "Extension": ".py", "SizeKB": 16.33, "Name": "performance_optimizer.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\performance_utils.py", "Extension": ".py", "SizeKB": 12.17, "Name": "performance_utils.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\query_utils.py", "Extension": ".py", "SizeKB": 15.03, "Name": "query_utils.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\core\\utils\\security.py", "Extension": ".py", "SizeKB": 7.04, "Name": "security.py", "Type": "File"}], "Name": "utils", "Type": "Folder"}], "Name": "core", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware", "Children": [{"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\caching_middleware.py", "Extension": ".py", "SizeKB": 17.0, "Name": "caching_middleware.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\context_middleware.py", "Extension": ".py", "SizeKB": 8.01, "Name": "context_middleware.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\logging_middleware.py", "Extension": ".py", "SizeKB": 10.37, "Name": "logging_middleware.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\rate_limiting_middleware.py", "Extension": ".py", "SizeKB": 13.57, "Name": "rate_limiting_middleware.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\middleware\\security_middleware.py", "Extension": ".py", "SizeKB": 18.88, "Name": "security_middleware.py", "Type": "File"}], "Name": "middleware", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\alembic.ini", "Extension": ".ini", "SizeKB": 3.11, "Name": "alembic.ini", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\app.py", "Extension": ".py", "SizeKB": 6.99, "Name": "app.py", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\src\\main.py", "Extension": ".py", "SizeKB": 11.11, "Name": "main.py", "Type": "File"}], "Name": "src", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\pyproject.toml", "Extension": ".toml", "SizeKB": 14.77, "Name": "pyproject.toml", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\server\\README.md", "Extension": ".md", "SizeKB": 0.0, "Name": "README.md", "Type": "File"}], "Name": "server", "Type": "Folder"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\dev_prompt-instructions.md", "Extension": ".md", "SizeKB": 7.4, "Name": "dev_prompt-instructions.md", "Type": "File"}, {"Path": "D:\\Projects\\ultimate-electrical-designer\\README.md", "Extension": ".md", "SizeKB": 0.0, "Name": "README.md", "Type": "File"}], "Name": "ultimate-electrical-designer", "Type": "Folder"}