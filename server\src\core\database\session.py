# backend/core/database/session.py
"""Database Session Management

This module handles SQLAlchemy session creation, management, and provides
dependency injection functions for FastAPI.
"""

from collections.abc import Generator
from contextlib import contextmanager
import time
import threading
from typing import Optional, Callable, Any

from sqlalchemy import Engine, event, text
from sqlalchemy.exc import SQLAlchemyError, DisconnectionError, OperationalError
from sqlalchemy.orm import Session, scoped_session, sessionmaker
from sqlalchemy.pool import StaticPool

try:
    from src.config.logging_config import logger
except ImportError:
    from src.config.logging_config import logger

from src.core.errors.unified_error_handler import handle_database_errors
from src.core.database.engine import get_engine

# Global session factory
_session_factory: sessionmaker | None = None
_scoped_session: scoped_session | None = None


@handle_database_errors("session_factory_creation")
def get_session_factory(engine: Engine | None = None) -> sessionmaker:
    """Get or create a session factory with enhanced configuration.

    Args:
        engine: Optional engine instance. If None, uses the global engine.

    Returns:
        SQLAlchemy sessionmaker instance

    """
    global _session_factory

    if _session_factory is None or engine is not None:
        if engine is None:
            engine = get_engine()

        # Configure session factory with production-optimized settings
        _session_factory = sessionmaker(
            bind=engine,
            autocommit=False,
            autoflush=False,  # Manual flush control for better performance
            expire_on_commit=False,  # Keep objects accessible after commit
            # Production optimizations for electrical design workloads
            class_=Session,
            # Query optimization settings
            query_cls=None,  # Use default Query class with optimizations
        )

        # Session factory configured successfully

        logger.debug("Enhanced session factory created")

    return _session_factory


def get_scoped_session(engine: Engine | None = None) -> scoped_session:
    """Get or create a scoped session for thread-safe operations.

    Args:
        engine: Optional engine instance. If None, uses the global engine.

    Returns:
        SQLAlchemy scoped_session instance

    """
    global _scoped_session

    if _scoped_session is None or engine is not None:
        session_factory = get_session_factory(engine)
        _scoped_session = scoped_session(session_factory)
        logger.debug("Scoped session created")

    return _scoped_session


@handle_database_errors("session_management")
@contextmanager
def get_db_session(
    max_retries: int = 3, retry_delay: float = 0.1
) -> Generator[Session, None, None]:
    """Context manager for database sessions with automatic cleanup and retry logic.

    Args:
        max_retries: Maximum number of retry attempts for transient errors
        retry_delay: Delay between retry attempts in seconds

    Yields:
        SQLAlchemy Session instance

    Example:
        with get_db_session() as session:
            user = session.query(User).first()

    """
    session_factory = get_session_factory()
    session = None
    last_exception = None

    for attempt in range(max_retries + 1):
        try:
            if session is None:
                session = session_factory()
                logger.debug(f"Database session created (attempt {attempt + 1})")

            yield session
            session.commit()
            logger.debug("Database session committed")
            return

        except (DisconnectionError, OperationalError) as e:
            # Transient errors that might be resolved with retry
            last_exception = e
            logger.warning(f"Transient database error on attempt {attempt + 1}: {e}")

            if session:
                session.rollback()
                session.close()
                session = None

            if attempt < max_retries:
                time.sleep(retry_delay * (2**attempt))  # Exponential backoff
                continue
            else:
                logger.error(
                    f"Max retries ({max_retries}) exceeded for transient error: {e}"
                )
                # Raise a more descriptive error for max retries exceeded
                from src.core.errors.exceptions import DatabaseError

                raise DatabaseError(
                    reason=f"Database session creation failed after {max_retries} retries: {e}",
                    original_exception=e,
                )

        except Exception as e:
            # Handle all other exceptions with rollback
            logger.error(f"Database session error: {e}")
            if session:
                session.rollback()
                logger.debug("Database session rolled back due to error")
            # Re-raise the exception to be handled by unified error handler
            raise

        finally:
            if session:
                session.close()
                logger.debug("Database session closed")


@handle_database_errors("session_savepoint_management")
@contextmanager
def get_db_session_with_savepoint() -> Generator[Session, None, None]:
    """Context manager for database sessions with savepoint support for nested transactions.

    Yields:
        SQLAlchemy Session instance with savepoint support

    Example:
        with get_db_session_with_savepoint() as session:
            # Outer transaction
            with session.begin_nested():  # Savepoint
                # Inner transaction that can be rolled back independently
                pass

    """
    with get_db_session() as session:
        # Begin a savepoint transaction
        with session.begin_nested():
            yield session


@handle_database_errors("fastapi_session_dependency")
def get_db() -> Generator[Session, None, None]:
    """FastAPI dependency function for database sessions.

    This function is designed to be used with FastAPI's Depends() system.

    Yields:
        SQLAlchemy Session instance

    Example:
        @app.get("/users/")
        def get_users(db: Session = Depends(get_db)):
            return db.query(User).all()

    """
    with get_db_session() as session:
        yield session


@handle_database_errors("production_session_optimization")
@contextmanager
def get_optimized_db_session(
    enable_query_cache: bool = True,
    batch_size: int = 1000,
    isolation_level: str = "READ_COMMITTED",
) -> Generator[Session, None, None]:
    """Context manager for production-optimized database sessions.

    Provides enhanced session configuration for high-performance electrical
    design calculations and bulk operations.

    Args:
        enable_query_cache: Enable query result caching for repeated calculations
        batch_size: Optimal batch size for bulk operations
        isolation_level: Transaction isolation level for consistency vs performance

    Yields:
        Optimized SQLAlchemy Session instance

    Example:
        with get_optimized_db_session(batch_size=500) as session:
            # Perform bulk electrical calculations
            results = session.execute(complex_calculation_query)
    """
    session_factory = get_session_factory()
    session = session_factory()

    try:
        # Apply production optimizations
        if enable_query_cache:
            # Enable query plan caching for repeated electrical calculations
            session.execute(text("SET query_cache_type = ON"))

        # Set optimal batch size for bulk operations
        session.execute(text(f"SET SESSION sql_mode = 'TRADITIONAL'"))

        # Configure transaction isolation for electrical design workloads
        session.execute(text(f"SET TRANSACTION ISOLATION LEVEL {isolation_level}"))

        # Optimize for electrical calculation workloads
        session.execute(text("SET SESSION optimizer_search_depth = 62"))
        session.execute(text("SET SESSION tmp_table_size = 268435456"))  # 256MB
        session.execute(text("SET SESSION max_heap_table_size = 268435456"))  # 256MB

        logger.debug(
            f"Optimized database session configured with batch_size={batch_size}"
        )

        yield session
        session.commit()
        logger.debug("Optimized database session committed successfully")

    except Exception as e:
        session.rollback()
        logger.error(f"Error in optimized database session: {e}")
        raise
    finally:
        session.close()
        logger.debug("Optimized database session closed")


@handle_database_errors("connection_health_monitoring")
def monitor_connection_health() -> dict[str, Any]:
    """Monitor database connection pool health and performance metrics.

    Provides comprehensive health monitoring for production database connections
    including pool status, connection metrics, and performance indicators.

    Returns:
        Dict containing connection health metrics and recommendations

    Example:
        health_status = monitor_connection_health()
        if health_status["pool_utilization"] > 0.8:
            logger.warning("High connection pool utilization detected")
    """
    engine = get_engine()
    pool = engine.pool

    # Collect connection pool metrics
    pool_metrics = {
        "pool_size": pool.size(),
        "checked_in_connections": pool.checkedin(),
        "checked_out_connections": pool.checkedout(),
        "overflow_connections": pool.overflow(),
        "invalid_connections": pool.invalid(),
    }

    # Calculate utilization metrics
    total_connections = (
        pool_metrics["checked_in_connections"] + pool_metrics["checked_out_connections"]
    )
    max_connections = pool.size() + pool.overflow()
    pool_utilization = total_connections / max_connections if max_connections > 0 else 0

    # Performance health indicators
    health_status = {
        "timestamp": time.time(),
        "pool_metrics": pool_metrics,
        "pool_utilization": round(pool_utilization, 3),
        "max_connections": max_connections,
        "health_score": _calculate_connection_health_score(
            pool_metrics, pool_utilization
        ),
        "recommendations": _generate_connection_recommendations(
            pool_metrics, pool_utilization
        ),
        "database_type": str(engine.url).split("://")[0],
    }

    # Test connection responsiveness
    try:
        start_time = time.time()
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        connection_latency = time.time() - start_time
        health_status["connection_latency_ms"] = round(connection_latency * 1000, 2)
        health_status["connection_responsive"] = (
            connection_latency < 1.0
        )  # Less than 1 second
    except Exception as e:
        health_status["connection_latency_ms"] = None
        health_status["connection_responsive"] = False
        health_status["connection_error"] = str(e)

    logger.debug(
        f"Connection health monitoring completed: {health_status['health_score']}/10"
    )
    return health_status


def _calculate_connection_health_score(
    pool_metrics: dict, pool_utilization: float
) -> int:
    """Calculate overall connection health score (0-10 scale)."""
    score = 10

    # Penalize high utilization
    if pool_utilization > 0.9:
        score -= 4
    elif pool_utilization > 0.8:
        score -= 2
    elif pool_utilization > 0.7:
        score -= 1

    # Penalize invalid connections
    if pool_metrics["invalid_connections"] > 0:
        score -= 2

    # Penalize if no available connections
    if pool_metrics["checked_in_connections"] == 0:
        score -= 1

    return max(0, score)


def _generate_connection_recommendations(
    pool_metrics: dict, pool_utilization: float
) -> list[str]:
    """Generate recommendations based on connection health metrics."""
    recommendations = []

    if pool_utilization > 0.8:
        recommendations.append("Consider increasing pool_size or max_overflow")

    if pool_metrics["invalid_connections"] > 0:
        recommendations.append("Invalid connections detected - check network stability")

    if (
        pool_metrics["checked_in_connections"] == 0
        and pool_metrics["checked_out_connections"] > 0
    ):
        recommendations.append("All connections in use - monitor for connection leaks")

    if pool_utilization < 0.2:
        recommendations.append(
            "Low utilization - consider reducing pool_size for resource efficiency"
        )

    if not recommendations:
        recommendations.append("Connection pool health is optimal")

    return recommendations


@handle_database_errors("table_creation")
def create_tables(engine: Engine | None = None) -> None:
    """Create all database tables defined in the models.

    Args:
        engine: Optional engine instance. If None, uses the global engine.

    Note:
        This function should only be used for development or initial setup.
        In production, use Alembic migrations instead.

    """
    if engine is None:
        engine = get_engine()

    # Import Base here to avoid circular imports
    from src.core.models.base import Base

    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except SQLAlchemyError as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


def drop_tables(engine: Engine | None = None) -> None:
    """Drop all database tables.

    Args:
        engine: Optional engine instance. If None, uses the global engine.

    Warning:
        This function will delete all data! Use with extreme caution.

    """
    if engine is None:
        engine = get_engine()

    # Import Base here to avoid circular imports
    from src.core.models.base import Base

    try:
        Base.metadata.drop_all(bind=engine)
        logger.warning("All database tables dropped")
    except SQLAlchemyError as e:
        logger.error(f"Failed to drop database tables: {e}")
        raise


def reset_session_factory() -> None:
    """Reset the global session factory. Useful for testing."""
    global _session_factory, _scoped_session

    if _scoped_session is not None:
        _scoped_session.remove()
        _scoped_session = None

    _session_factory = None
    logger.debug("Session factory reset")
