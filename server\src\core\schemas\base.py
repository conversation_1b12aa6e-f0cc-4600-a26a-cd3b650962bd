# src/core/schemas/base.py
"""
Base schemas for Ultimate Electrical Designer.

This module provides base Pydantic models and mixins used throughout
the application for consistent validation and serialization.
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime


class BaseSchema(BaseModel):
    """Base schema with common configuration."""

    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        use_enum_values=True,
        extra="forbid",
    )


class TimestampMixin(BaseModel):
    """Mixin for timestamp fields."""

    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class BaseSoftDeleteSchema(BaseSchema):
    """Base schema with soft delete functionality."""

    is_deleted: bool = Field(False, description="Whether the record is soft deleted")
    deleted_at: Optional[datetime] = Field(None, description="Soft deletion timestamp")


class PaginationSchema(BaseModel):
    """Schema for pagination parameters."""

    page: int = Field(1, ge=1, description="Page number (1-based)")
    size: int = Field(20, ge=1, le=100, description="Items per page")
    total: Optional[int] = Field(None, description="Total number of items")
    pages: Optional[int] = Field(None, description="Total number of pages")


class PaginatedResponseSchema(BaseModel):
    """Generic paginated response schema."""

    items: List[Any] = Field(..., description="List of items")
    pagination: PaginationSchema = Field(..., description="Pagination information")


__all__ = [
    "BaseSchema",
    "TimestampMixin",
    "BaseSoftDeleteSchema",
    "PaginationSchema",
    "PaginatedResponseSchema",
]
