# src/api/main_router.py
"""Main API Router Configuration.

This module configures the main API router for the Ultimate Electrical Designer
backend application. It aggregates all API version routers and provides a
centralized routing configuration for the FastAPI application.

The router includes all v1 API endpoints for electrical calculations, component
management, project management, and reporting functionality.
"""

from fastapi import APIRouter

from src.core.monitoring.unified_performance_monitor import monitor_utility_performance
from src.config.logging_config import logger
from src.api.v1 import v1_router

@monitor_utility_performance("main_api_router_initialization")
def create_main_api_router() -> APIRouter:
    """Create and configure the main API router with all sub-routers.

    Returns:
        APIRouter: Configured main API router with all endpoints
    """
    logger.info("Initializing main API router with unified patterns")

    api_router = APIRouter()

    # Include v1 API router with all endpoints
    api_router.include_router(v1_router, tags=["API v1"])

    logger.info("Main API router initialized successfully with all sub-routers")
    logger.debug("Main API router includes: v1 (health, auth, users)")
    return api_router


# Create the main API router instance
api_router = create_main_api_router()
