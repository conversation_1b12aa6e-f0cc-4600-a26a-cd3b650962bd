# src/core/services/health_service.py
"""Health Check Service for Ultimate Electrical Designer.

This module provides comprehensive health monitoring capabilities
for the application, including database connectivity, system metrics,
and service status monitoring.
"""

import time
import psutil
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional

from src.config.logging_config import logger
from src.config.settings import settings
from src.core.database.session import monitor_connection_health
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.schemas.health import (
    DatabaseHealthSchema,
    ServiceHealthSchema,
    SystemMetricsSchema,
    HealthCheckResponseSchema,
    SimpleHealthResponseSchema,
)


class HealthService:
    """Service for comprehensive system health monitoring.
    
    Provides health check capabilities including database connectivity,
    system metrics, service status, and overall system health assessment.
    """
    
    def __init__(self):
        """Initialize the health service."""
        self.start_time = time.time()
        logger.debug("Health service initialized")
    
    @handle_service_errors("get_comprehensive_health")
    @monitor_service_performance("get_comprehensive_health")
    def get_comprehensive_health(self) -> HealthCheckResponseSchema:
        """Get comprehensive system health status.
        
        Returns:
            HealthCheckResponseSchema: Complete health status information
        """
        start_time = time.time()
        logger.debug("Starting comprehensive health check")
        
        # Get database health
        database_health = self._get_database_health()
        
        # Get system metrics
        system_metrics = self._get_system_metrics()
        
        # Get service health
        services = self._get_services_health()
        
        # Calculate overall health score
        health_score = self._calculate_overall_health_score(
            database_health, system_metrics, services
        )
        
        # Identify critical issues and warnings
        critical_issues, warnings = self._analyze_health_issues(
            database_health, system_metrics, services
        )
        
        # Calculate response time
        response_time_ms = round((time.time() - start_time) * 1000, 2)
        
        # Determine overall status
        overall_status = self._determine_overall_status(health_score, critical_issues)
        
        health_response = HealthCheckResponseSchema(
            status=overall_status,
            timestamp=datetime.now(timezone.utc),
            version=settings.APP_VERSION,
            environment=settings.ENVIRONMENT,
            uptime_seconds=round(time.time() - self.start_time, 2),
            database=database_health,
            services=services,
            system_metrics=system_metrics,
            health_score=health_score,
            critical_issues=critical_issues,
            warnings=warnings,
            checks_performed=[
                "database_connectivity",
                "system_metrics",
                "service_status",
                "performance_metrics"
            ],
            response_time_ms=response_time_ms
        )
        
        logger.info(f"Comprehensive health check completed: {overall_status} (score: {health_score}/10)")
        return health_response
    
    @handle_service_errors("get_simple_health")
    @monitor_service_performance("get_simple_health")
    def get_simple_health(self) -> SimpleHealthResponseSchema:
        """Get simplified health status for basic monitoring.
        
        Returns:
            SimpleHealthResponseSchema: Basic health status information
        """
        logger.debug("Starting simple health check")
        
        # Get basic database status
        try:
            db_health = monitor_connection_health()
            database_status = "healthy" if db_health.get("connection_responsive", False) else "unhealthy"
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            database_status = "unhealthy"
        
        # Determine overall status
        overall_status = "healthy" if database_status == "healthy" else "unhealthy"
        
        health_response = SimpleHealthResponseSchema(
            status=overall_status,
            timestamp=datetime.now(timezone.utc),
            version=settings.APP_VERSION,
            database_status=database_status,
            uptime_seconds=round(time.time() - self.start_time, 2)
        )
        
        logger.debug(f"Simple health check completed: {overall_status}")
        return health_response
    
    def _get_database_health(self) -> DatabaseHealthSchema:
        """Get database health status."""
        try:
            db_health = monitor_connection_health()
            
            return DatabaseHealthSchema(
                status="healthy" if db_health.get("connection_responsive", False) else "unhealthy",
                connection_responsive=db_health.get("connection_responsive", False),
                connection_latency_ms=db_health.get("connection_latency_ms"),
                pool_utilization=db_health.get("pool_utilization", 0.0),
                pool_metrics=db_health.get("pool_metrics", {}),
                health_score=db_health.get("health_score", 0),
                recommendations=db_health.get("recommendations", []),
                database_type=db_health.get("database_type", "unknown"),
                connection_error=db_health.get("connection_error")
            )
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return DatabaseHealthSchema(
                status="unhealthy",
                connection_responsive=False,
                connection_latency_ms=None,
                pool_utilization=0.0,
                pool_metrics={},
                health_score=0,
                recommendations=["Database connection failed"],
                database_type="unknown",
                connection_error=str(e)
            )
    
    def _get_system_metrics(self) -> Optional[SystemMetricsSchema]:
        """Get system performance metrics."""
        try:
            # Get memory usage
            memory = psutil.virtual_memory()
            memory_usage_mb = round(memory.used / (1024 * 1024), 2)
            
            # Get CPU usage
            cpu_usage = psutil.cpu_percent(interval=0.1)
            
            # Get disk usage
            disk = psutil.disk_usage('/')
            disk_usage_percent = round((disk.used / disk.total) * 100, 2)
            
            return SystemMetricsSchema(
                memory_usage_mb=memory_usage_mb,
                cpu_usage_percent=cpu_usage,
                disk_usage_percent=disk_usage_percent,
                active_connections=None,  # Would need connection tracking
                request_count_last_minute=None  # Would need request tracking
            )
        except Exception as e:
            logger.warning(f"System metrics collection failed: {e}")
            return None
    
    def _get_services_health(self) -> List[ServiceHealthSchema]:
        """Get health status of individual services."""
        services = []
        
        # Main application service
        services.append(ServiceHealthSchema(
            name="ultimate-electrical-designer-api",
            status="healthy",
            version=settings.APP_VERSION,
            uptime_seconds=round(time.time() - self.start_time, 2),
            last_check=datetime.now(timezone.utc),
            details={"environment": settings.ENVIRONMENT}
        ))
        
        return services
    
    def _calculate_overall_health_score(
        self, 
        database_health: DatabaseHealthSchema,
        system_metrics: Optional[SystemMetricsSchema],
        services: List[ServiceHealthSchema]
    ) -> int:
        """Calculate overall system health score (0-10)."""
        score = 10
        
        # Database health contributes 50% to overall score
        db_score = database_health.health_score
        score = int(score * 0.5 + db_score * 0.5)
        
        # System metrics contribute to score
        if system_metrics:
            if system_metrics.memory_usage_mb and system_metrics.memory_usage_mb > 500:  # 500MB threshold
                score -= 1
            if system_metrics.cpu_usage_percent and system_metrics.cpu_usage_percent > 80:
                score -= 1
            if system_metrics.disk_usage_percent and system_metrics.disk_usage_percent > 90:
                score -= 2
        
        # Service health
        unhealthy_services = [s for s in services if s.status != "healthy"]
        score -= len(unhealthy_services)
        
        return max(0, min(10, score))
    
    def _analyze_health_issues(
        self,
        database_health: DatabaseHealthSchema,
        system_metrics: Optional[SystemMetricsSchema],
        services: List[ServiceHealthSchema]
    ) -> tuple[List[str], List[str]]:
        """Analyze health data to identify critical issues and warnings."""
        critical_issues = []
        warnings = []
        
        # Database issues
        if not database_health.connection_responsive:
            critical_issues.append("Database connection is not responsive")
        elif database_health.pool_utilization > 0.9:
            critical_issues.append("Database connection pool utilization is critically high")
        elif database_health.pool_utilization > 0.8:
            warnings.append("Database connection pool utilization is high")
        
        # System metrics issues
        if system_metrics:
            if system_metrics.cpu_usage_percent and system_metrics.cpu_usage_percent > 90:
                critical_issues.append("CPU usage is critically high")
            elif system_metrics.cpu_usage_percent and system_metrics.cpu_usage_percent > 80:
                warnings.append("CPU usage is high")
            
            if system_metrics.memory_usage_mb and system_metrics.memory_usage_mb > 1000:  # 1GB
                warnings.append("Memory usage is high")
            
            if system_metrics.disk_usage_percent and system_metrics.disk_usage_percent > 95:
                critical_issues.append("Disk usage is critically high")
            elif system_metrics.disk_usage_percent and system_metrics.disk_usage_percent > 85:
                warnings.append("Disk usage is high")
        
        # Service issues
        for service in services:
            if service.status == "unhealthy":
                critical_issues.append(f"Service {service.name} is unhealthy")
            elif service.status == "degraded":
                warnings.append(f"Service {service.name} is degraded")
        
        return critical_issues, warnings
    
    def _determine_overall_status(self, health_score: int, critical_issues: List[str]) -> str:
        """Determine overall system status based on health score and issues."""
        if critical_issues:
            return "unhealthy"
        elif health_score >= 8:
            return "healthy"
        elif health_score >= 6:
            return "degraded"
        else:
            return "unhealthy"


# Global health service instance
health_service = HealthService()


def get_health_service() -> HealthService:
    """Get the global health service instance.
    
    Returns:
        HealthService: The health service instance
    """
    return health_service
