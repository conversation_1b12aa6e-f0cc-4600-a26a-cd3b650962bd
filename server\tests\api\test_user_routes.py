# tests/api/test_user_routes.py
"""Tests for user management API endpoints."""

import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock

from src.core.enums import UserRole
from src.core.schemas.general.user_schemas import UserCreateSchema


class TestUserRoutes:
    """Test suite for user management endpoints."""

    @pytest.fixture
    def admin_user_data(self):
        """Create admin user data."""
        return {
            "name": "Admin User",
            "email": "<EMAIL>",
            "password": "AdminPass123",
            "role": UserRole.ADMIN,
            "is_active": True
        }

    @pytest.fixture
    def regular_user_data(self):
        """Create regular user data."""
        return {
            "name": "Regular User",
            "email": "<EMAIL>",
            "password": "UserPass123",
            "role": UserRole.VIEWER,
            "is_active": True
        }

    @pytest.fixture
    def created_admin_user(self, db_session, admin_user_data):
        """Create an admin user in the database."""
        from src.core.services.general.user_service import UserService
        from src.core.repositories.general.user_repository import UserRepository
        
        user_service = UserService(db_session)
        
        user_create = UserCreateSchema(**admin_user_data)
        user = user_service.create_user(user_create)
        return user

    @pytest.fixture
    def created_regular_user(self, db_session, regular_user_data):
        """Create a regular user in the database."""
        from src.core.services.general.user_service import UserService
        from src.core.repositories.general.user_repository import UserRepository
        
        user_service = UserService(db_session)
        
        user_create = UserCreateSchema(**regular_user_data)
        user = user_service.create_user(user_create)
        return user

    @pytest.fixture
    def admin_token(self, client: TestClient, created_admin_user):
        """Get admin authentication token."""
        login_data = {
            "username": created_admin_user.email,
            "password": "AdminPass123"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        return response.json()["access_token"]

    @pytest.fixture
    def user_token(self, client: TestClient, created_regular_user):
        """Get regular user authentication token."""
        login_data = {
            "username": created_regular_user.email,
            "password": "UserPass123"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        return response.json()["access_token"]

    def test_get_current_user_profile(self, client: TestClient, user_token, created_regular_user):
        """Test getting current user's profile."""
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.get("/api/v1/users/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == created_regular_user.id
        assert data["name"] == created_regular_user.name
        assert data["email"] == created_regular_user.email
        assert data["role"] == created_regular_user.role
        assert "password_hash" not in data

    def test_get_current_user_profile_unauthenticated(self, client: TestClient):
        """Test getting current user profile without authentication."""
        response = client.get("/api/v1/users/me")
        
        assert response.status_code == 401

    def test_update_current_user_profile(self, client: TestClient, user_token):
        """Test updating current user's profile."""
        update_data = {
            "name": "Updated User Name"
        }
        
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.put("/api/v1/users/me", json=update_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["name"] == "Updated User Name"

    def test_update_current_user_profile_forbidden_fields(self, client: TestClient, user_token):
        """Test updating forbidden fields in current user's profile."""
        update_data = {
            "role": UserRole.ADMIN  # Users shouldn't be able to change their role
        }
        
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.put("/api/v1/users/me", json=update_data, headers=headers)
        
        assert response.status_code == 403

    def test_get_users_summary_admin(self, client: TestClient, admin_token, created_regular_user):
        """Test getting users summary as admin."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get("/api/v1/users/summary", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        if data:
            user_summary = data[0]
            required_fields = ["id", "name", "email", "role", "is_active", "created_at"]
            for field in required_fields:
                assert field in user_summary

    def test_get_users_summary_non_admin(self, client: TestClient, user_token):
        """Test getting users summary as non-admin."""
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.get("/api/v1/users/summary", headers=headers)
        
        assert response.status_code == 403

    def test_get_users_summary_with_limit(self, client: TestClient, admin_token):
        """Test getting users summary with limit parameter."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get("/api/v1/users/summary?limit=5", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) <= 5

    def test_get_user_profile_by_id_admin(self, client: TestClient, admin_token, created_regular_user):
        """Test getting user profile by ID as admin."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get(f"/api/v1/users/{created_regular_user.id}/profile", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == created_regular_user.id
        assert data["email"] == created_regular_user.email

    def test_get_user_profile_by_id_non_admin(self, client: TestClient, user_token, created_regular_user):
        """Test getting user profile by ID as non-admin."""
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.get(f"/api/v1/users/{created_regular_user.id}/profile", headers=headers)
        
        assert response.status_code == 403

    def test_get_user_profile_by_id_not_found(self, client: TestClient, admin_token):
        """Test getting user profile for non-existent user."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get("/api/v1/users/99999/profile", headers=headers)
        
        assert response.status_code == 404

    def test_activate_user_account_admin(self, client: TestClient, admin_token, created_regular_user):
        """Test activating user account as admin."""
        # First deactivate the user
        headers = {"Authorization": f"Bearer {admin_token}"}
        client.post(f"/api/v1/users/{created_regular_user.id}/deactivate", headers=headers)
        
        # Then activate
        response = client.post(f"/api/v1/users/{created_regular_user.id}/activate", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["is_active"] is True

    def test_activate_user_account_non_admin(self, client: TestClient, user_token, created_regular_user):
        """Test activating user account as non-admin."""
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.post(f"/api/v1/users/{created_regular_user.id}/activate", headers=headers)
        
        assert response.status_code == 403

    def test_deactivate_user_account_admin(self, client: TestClient, admin_token, created_regular_user):
        """Test deactivating user account as admin."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.post(f"/api/v1/users/{created_regular_user.id}/deactivate", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["is_active"] is False

    def test_deactivate_own_account_admin(self, client: TestClient, admin_token, created_admin_user):
        """Test admin trying to deactivate their own account."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.post(f"/api/v1/users/{created_admin_user.id}/deactivate", headers=headers)
        
        assert response.status_code == 400
        data = response.json()
        assert "Cannot deactivate your own account" in data["detail"]

    def test_deactivate_user_account_non_admin(self, client: TestClient, user_token, created_regular_user):
        """Test deactivating user account as non-admin."""
        headers = {"Authorization": f"Bearer {user_token}"}
        response = client.post(f"/api/v1/users/{created_regular_user.id}/deactivate", headers=headers)
        
        assert response.status_code == 403

    def test_user_crud_operations_admin(self, client: TestClient, admin_token):
        """Test CRUD operations for users as admin."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Create user
        user_data = {
            "name": "New Test User",
            "email": "<EMAIL>",
            "password": "NewUserPass123",
            "role": UserRole.VIEWER,
            "is_active": True
        }
        
        create_response = client.post("/api/v1/users/", json=user_data, headers=headers)
        assert create_response.status_code == 201
        created_user = create_response.json()
        user_id = created_user["id"]
        
        # Read user
        read_response = client.get(f"/api/v1/users/{user_id}", headers=headers)
        assert read_response.status_code == 200
        
        # Update user
        update_data = {"name": "Updated Test User"}
        update_response = client.put(f"/api/v1/users/{user_id}", json=update_data, headers=headers)
        assert update_response.status_code == 200
        
        # Delete user
        delete_response = client.delete(f"/api/v1/users/{user_id}", headers=headers)
        assert delete_response.status_code == 204

    def test_user_crud_operations_non_admin(self, client: TestClient, user_token):
        """Test CRUD operations for users as non-admin."""
        headers = {"Authorization": f"Bearer {user_token}"}
        
        # All CRUD operations should be forbidden for non-admin users
        user_data = {
            "name": "New Test User",
            "email": "<EMAIL>",
            "password": "NewUserPass123",
            "role": UserRole.VIEWER,
            "is_active": True
        }
        
        create_response = client.post("/api/v1/users/", json=user_data, headers=headers)
        assert create_response.status_code == 403

    def test_user_list_pagination_admin(self, client: TestClient, admin_token):
        """Test user list pagination as admin."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = client.get("/api/v1/users/?page=1&page_size=10", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # Should return paginated response structure
        expected_fields = ["items", "total", "page", "page_size", "total_pages"]
        for field in expected_fields:
            assert field in data

    def test_user_validation_errors(self, client: TestClient, admin_token):
        """Test user creation with validation errors."""
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # Missing required fields
        invalid_user_data = {
            "name": "Test User"
            # Missing email, password, etc.
        }
        
        response = client.post("/api/v1/users/", json=invalid_user_data, headers=headers)
        assert response.status_code == 422

    def test_user_endpoints_performance_monitoring(self, client: TestClient, user_token):
        """Test that user endpoints include performance monitoring."""
        headers = {"Authorization": f"Bearer {user_token}"}
        
        # Performance monitoring should be transparent to the client
        response = client.get("/api/v1/users/me", headers=headers)
        
        assert response.status_code == 200
        # Performance metrics are logged, not returned in response
