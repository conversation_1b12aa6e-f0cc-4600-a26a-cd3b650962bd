# !/usr/bin/env python3
"""
User schemas for Ultimate Electrical Designer.

This module provides Pydantic schemas for user management operations,
supporting authentication, authorization, and user profile management.
"""

from typing import Optional, List
from pydantic import BaseModel, Field, EmailStr
from datetime import datetime

from src.core.schemas.base import BaseSchema, TimestampMixin, PaginatedResponseSchema
from src.core.enums import UserRole

class UserBaseSchema(BaseSchema):
    """Base schema for users."""

    name: str = Field(..., min_length=3, max_length=50, description="User name")
    email: EmailStr = Field(..., description="Email address")
    role: UserRole = Field(UserRole.VIEWER, description="User role")
    is_active: bool = Field(True, description="Whether user is active")


class UserCreateSchema(UserBaseSchema):
    """Schema for creating users."""

    password: str = Field(..., min_length=8, description="Password")


class UserUpdateSchema(BaseModel):
    """Schema for updating users."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=50, description="User name"
    )
    email: Optional[EmailStr] = Field(None, description="Email address")
    role: Optional[UserRole] = Field(None, description="User role")
    is_active: Optional[bool] = Field(None, description="Whether user is active")
    password: Optional[str] = Field(None, min_length=8, description="New password")


class UserReadSchema(UserBaseSchema, TimestampMixin):
    """Schema for reading users."""

    id: int = Field(..., description="Unique identifier")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class UserPaginatedResponseSchema(PaginatedResponseSchema):
    """Paginated response schema for users."""

    items: List[UserReadSchema] = Field(..., description="List of users")


class LoginRequestSchema(BaseModel):
    """Schema for login requests."""

    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="Password")


class LoginResponseSchema(BaseModel):
    """Schema for login responses."""

    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: UserReadSchema = Field(..., description="User information")


class LogoutResponseSchema(BaseModel):
    """Schema for logout responses."""

    message: str = Field(
        "Successfully logged out", description="Logout confirmation message"
    )
    logged_out_at: datetime = Field(..., description="Logout timestamp")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class PasswordChangeRequestSchema(BaseModel):
    """Schema for password change requests."""

    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Confirm new password")

    def validate_passwords_match(self):
        """Validate that new password and confirmation match."""
        if self.new_password != self.confirm_password:
            raise ValueError("New password and confirmation do not match")
        return self


class PasswordChangeResponseSchema(BaseModel):
    """Schema for password change responses."""

    message: str = Field("Password changed successfully", description="Success message")
    changed_at: datetime = Field(..., description="Password change timestamp")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class UserPreferenceBaseSchema(BaseSchema):
    """Base schema for user preferences."""

    theme: str = Field("light", description="UI theme preference")
    language: str = Field("en", description="Language preference")
    timezone: str = Field("UTC", description="Timezone preference")
    date_format: str = Field("YYYY-MM-DD", description="Date format preference")
    time_format: str = Field("24h", description="Time format preference")
    units_system: str = Field("metric", description="Units system preference")
    notifications_enabled: bool = Field(
        True, description="Whether notifications are enabled"
    )
    auto_save_interval: int = Field(300, description="Auto-save interval in seconds")


class UserPreferenceCreateSchema(UserPreferenceBaseSchema):
    """Schema for creating user preferences."""

    user_id: int = Field(..., description="Associated user ID")


class UserPreferenceUpdateSchema(BaseModel):
    """Schema for updating user preferences."""

    theme: Optional[str] = Field(None, description="UI theme preference")
    language: Optional[str] = Field(None, description="Language preference")
    timezone: Optional[str] = Field(None, description="Timezone preference")
    date_format: Optional[str] = Field(None, description="Date format preference")
    time_format: Optional[str] = Field(None, description="Time format preference")
    units_system: Optional[str] = Field(None, description="Units system preference")
    notifications_enabled: Optional[bool] = Field(
        None, description="Whether notifications are enabled"
    )
    auto_save_interval: Optional[int] = Field(
        None, description="Auto-save interval in seconds"
    )


class UserPreferenceReadSchema(UserPreferenceBaseSchema, TimestampMixin):
    """Schema for reading user preferences."""

    id: int = Field(..., description="Unique identifier")
    user_id: int = Field(..., description="Associated user ID")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class UserSummarySchema(BaseSchema):
    """Schema for user summaries."""

    id: int = Field(..., description="Unique identifier")
    name: str = Field(..., description="User name")
    email: EmailStr = Field(..., description="Email address")
    role: UserRole = Field(..., description="User role")
    is_active: bool = Field(..., description="Whether user is active")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    created_at: datetime = Field(..., description="Creation timestamp")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class PasswordResetRequestSchema(BaseModel):
    """Schema for password reset requests."""

    email: EmailStr = Field(..., description="Email address for password reset")


class PasswordResetConfirmSchema(BaseModel):
    """Schema for password reset confirmation."""

    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Confirm new password")

    def validate_passwords_match(self):
        """Validate that new password and confirmation match."""
        if self.new_password != self.confirm_password:
            raise ValueError("New password and confirmation do not match")
        return self


class PasswordResetResponseSchema(BaseModel):
    """Schema for password reset responses."""

    message: str = Field(..., description="Password reset response message")
    reset_token_sent: bool = Field(..., description="Whether reset token was sent")
    expires_at: Optional[datetime] = Field(None, description="Token expiration time")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class UserActivityLogSchema(BaseSchema):
    """Schema for user activity logs."""

    id: int = Field(..., description="Unique identifier")
    user_id: int = Field(..., description="User ID")
    action: str = Field(..., description="Action performed")
    resource: Optional[str] = Field(None, description="Resource affected")
    ip_address: Optional[str] = Field(None, description="IP address")
    user_agent: Optional[str] = Field(None, description="User agent")
    timestamp: datetime = Field(..., description="Action timestamp")
    success: bool = Field(..., description="Whether action was successful")
    details: Optional[dict] = Field(None, description="Additional details")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class UserSessionSchema(BaseSchema):
    """Schema for user sessions."""

    id: str = Field(..., description="Session ID")
    user_id: int = Field(..., description="User ID")
    ip_address: Optional[str] = Field(None, description="IP address")
    user_agent: Optional[str] = Field(None, description="User agent")
    created_at: datetime = Field(..., description="Session creation time")
    last_activity: datetime = Field(..., description="Last activity time")
    expires_at: datetime = Field(..., description="Session expiration time")
    is_active: bool = Field(..., description="Whether session is active")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


__all__ = [
    "UserBaseSchema",
    "UserCreateSchema",
    "UserUpdateSchema",
    "UserReadSchema",
    "UserPaginatedResponseSchema",
    "LoginRequestSchema",
    "LoginResponseSchema",
    "LogoutResponseSchema",
    "PasswordChangeRequestSchema",
    "PasswordChangeResponseSchema",
    "UserPreferenceBaseSchema",
    "UserPreferenceCreateSchema",
    "UserPreferenceUpdateSchema",
    "UserPreferenceReadSchema",
    "UserSummarySchema",
    "PasswordResetRequestSchema",
    "PasswordResetConfirmSchema",
    "PasswordResetResponseSchema",
    "UserActivityLogSchema",
    "UserSessionSchema",
]
